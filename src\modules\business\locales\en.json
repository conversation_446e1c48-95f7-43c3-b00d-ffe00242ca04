{"business": {"title": "Business Management", "description": "Manage your business activities", "common": {"status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "blocked": "Blocked", "draft": "Draft"}, "orderStatus": {"pending": "Pending", "processing": "Processing", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "completed": "Completed"}, "paymentStatus": {"paid": "Paid", "pending": "Pending Payment", "unpaid": "Unpaid", "failed": "Payment Failed", "partiallyPaid": "Partially Paid", "refunded": "Refunded"}, "shippingStatus": {"title": "Shipping Status", "pending": "Pending", "preparing": "Preparing", "shipped": "Shipped", "inTransit": "In Transit", "sorting": "Sorting", "delivered": "Delivered", "deliveryFailed": "Delivery Failed", "returning": "Returning", "cancelled": "Cancelled"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "back": "Back", "next": "Next", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "add": "Add", "remove": "Remove", "upload": "Upload", "download": "Download", "view": "View", "details": "Details", "actions": "Actions"}, "form": {"name": "Full Name", "email": "Email", "phone": "Phone Number", "address": "Address", "description": "Description", "status": "Status", "tags": "Tags", "notes": "Notes"}, "messages": {"createSuccess": "Created successfully", "createError": "Error occurred while creating", "updateSuccess": "Updated successfully", "updateError": "Error occurred while updating", "deleteSuccess": "Deleted successfully", "deleteError": "Error occurred while deleting", "bulkDeleteSuccess": "Successfully deleted {{count}} items", "bulkDeleteError": "Error occurred while deleting", "loadError": "Error loading data"}}, "customer": {"title": "Customers", "description": "Manage customer information", "add": "Add Customer", "edit": "Edit Customer", "view": "View Customer Details", "addForm": "Add New Customer", "editForm": "Edit Customer Information", "detailForm": "Customer Details", "totalCustomers": "Total Customers", "manage": "Manage Customers", "platform": "Platform", "timezone": "Timezone", "name": "Customer Name", "totalOrders": "Total Orders", "totalSpent": "Total Spent", "potentialScore": "Potential Score", "form": {"name": "Full Name", "namePlaceholder": "Enter customer full name", "email": "Email", "emailPlaceholder": "Enter email address", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "tags": "Customer Tags", "tagsPlaceholder": "Enter tag and press Enter", "address": "Address", "addressPlaceholder": "Enter address", "addressNote": "Address Note", "addressNotePlaceholder": "e.g., Office, Home, Company...", "avatar": "Avatar"}, "status": {"active": "Active", "inactive": "Inactive", "blocked": "Blocked"}, "detail": {"generalInfo": "General Information", "social": "Social", "customFields": "Custom Fields", "statusField": "Status", "orders": "Orders", "activities": "Activities", "overview": "Overview", "totalOrders": "Total Orders", "totalSpent": "Total Spent", "averageOrderValue": "Average Order Value", "lastOrderDate": "Last Order", "customerSince": "Customer Since", "interactionChannels": "Interaction Channels", "orderHistory": "Order History", "activityLog": "Activity Log", "socialProfiles": "Social Profiles", "customFieldValues": "Custom Field Values", "noData": "No data available", "noOrders": "No orders yet", "noOrdersDesc": "This customer has not placed any orders yet", "noActivities": "No activities yet", "noActivitiesDesc": "This customer has no recorded activities yet", "noInteractions": "No interactions yet", "allActivities": "All Activities", "revenue": "Revenue", "interactions": "Interactions", "topChannels": "Top Message Channels", "topDevices": "Top Customer Devices", "interactedFlows": "Interacted Flows", "interactedCampaigns": "Interacted Campaigns", "orderList": "Order History", "orderCode": "Order Code", "orderDate": "Order Date", "paymentMethod": "Payment Method", "deliveryStatus": "Delivery Status", "shippingStatus": "Shipping Status", "paymentStatus": "Payment Status", "orderStatus": "Order Status", "source": "Source", "totalAmount": "Total Amount", "flowName": "Flow Name", "lastInteraction": "Last Interaction", "campaignName": "Campaign Name", "interactionType": "Interaction Type", "sent": "<PERSON><PERSON>", "opened": "Opened", "clicked": "Clicked"}, "activity": {"type": "Activity Type", "date": "Date", "details": "Details", "moreDetails": "more details", "types": {"order": "Order", "login": "<PERSON><PERSON>", "support": "Support", "review": "Review"}}, "order": {"cancel": "Cancel Order", "status": {"pending": "Pending", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled"}}, "overview": {"flowCount": "Flow Count", "campaignCount": "Campaign Count", "sequenceCount": "Sequence Count", "interactions": "Interactions", "revenue": "Revenue", "flows": "Flows", "campaigns": "Campaigns", "sequences": "Sequences", "noChannelData": "No channel data available", "noDeviceData": "No device data available", "topChannels": "Top Message Channels", "topDevices": "Top Customer Devices"}, "social": {"platform": "Platform", "username": "Username", "link": "Link", "editDescription": "Enter username or link of the account on {{platform}}", "save": "Save"}, "interaction": {"type": "Interaction Type", "date": "Date", "details": "Details", "moreDetails": "more details", "title": "Interactions", "types": {"email": "Email", "phone": "Phone", "chat": "Cha<PERSON>", "social": "Social", "meeting": "Meeting"}}, "messages": {"createSuccess": "Customer created successfully", "createError": "Error creating customer", "updateSuccess": "Customer updated successfully", "updateError": "Error updating customer", "deleteSuccess": "Customer deleted successfully", "deleteError": "Error deleting customer", "bulkDeleteSuccess": "Successfully deleted {{count}} customers", "bulkDeleteError": "Error deleting customers"}, "bulkDeleteConfirmation": "Are you sure you want to delete {{count}} selected customers?", "import": {"title": "Import Customers", "steps": {"upload": "Upload", "mapping": "Map Columns", "preview": "Preview", "importing": "Importing"}, "upload": {"title": "Upload Excel file", "description": "Select Excel file or enter URL to import customer list", "fromFile": "From file", "fromUrl": "From URL", "dragDrop": "Drag and drop file here or click to select", "supportedFormats": "Support: .xlsx, .xls, .csv (max 10MB)", "selectFile": "Select file", "hasHeader": "File has header row", "urlTitle": "Import from URL", "urlPlaceholder": "Enter Excel file URL...", "excelUrl": "Excel file URL", "sheetName": "Sheet name", "sheetNamePlaceholder": "Enter sheet name (leave empty for first sheet)", "sheetNameHelp": "Leave empty to use the first sheet in the file", "loading": "Loading...", "loadFromUrl": "Load from URL", "dragDropTitle": "Drag and drop files here", "dragDropDescription": "or click to select files from your computer"}, "mapping": {"title": "Map Column Data", "description": "Select Excel columns corresponding to each customer field", "columnMapping": "Column Mapping", "selectField": "Select Field", "skipColumn": "Skip this column", "requiredField": "Required field", "dataPreview": "Data Preview", "previewData": "Show sample data", "previewNote": "Showing first 5 rows", "validationErrors": "Validation Errors", "errors": {"requiredFieldMissing": "Missing required field: {{field}}", "duplicateMapping": "Duplicate field mapping detected"}}, "preview": {"title": "Preview Import Data", "description": "Review data before importing to system", "totalRows": "Total Rows", "validRows": "Valid <PERSON>s", "invalidRows": "Invalid Rows", "validationWarnings": "Validation Warnings", "dataPreview": "Data Preview", "showingFirst10": "Showing first 10 rows", "importOptions": "Import Options", "skipInvalidRows": "Skip invalid rows", "sendWelcomeEmail": "Send welcome email", "startImport": "Start Import", "row": "Row"}, "progress": {"importing": "Importing data", "pleaseWait": "Please wait while system processes data", "processing": "Processing", "imported": "Imported", "errors": "Errors", "recentErrors": "Recent Errors"}, "complete": {"title": "Import Complete", "description": "Import process completed successfully", "totalProcessed": "Total Processed", "successfullyImported": "Successfully Imported", "failed": "Failed", "errorDetails": "<PERSON><PERSON><PERSON>", "nextSteps": "Next Steps", "reviewCustomers": "Review customer list", "setupSegments": "Setup customer segments", "createCampaigns": "Create marketing campaigns", "viewCustomers": "View Customers", "successMessage": "Successfully imported {{count}} customers!"}, "validation": {"nameRequired": "Customer name is required", "invalidEmail": "Invalid email", "invalidPhone": "Invalid phone number"}, "errors": {"fileRequired": "Please select a file", "urlRequired": "Please enter file URL", "invalidUrl": "Invalid URL format", "fileTooLarge": "File too large (max 10MB)", "unsupportedFormat": "Unsupported file format. Please use .xlsx, .xls, or .csv", "loadFailed": "Failed to load file", "networkError": "Network error occurred", "parseError": "Failed to parse file content", "emptyFile": "File is empty or has no data", "invalidFile": "Invalid or corrupted file", "readError": "Error reading file", "invalidFileType": "Unsupported file format", "urlFetchError": "Cannot fetch file from URL", "urlLoadError": "Error loading file from URL", "invalidSheet": "Invalid sheet name", "sheetNotFound": "Sheet not found in file"}}}, "product": {"title": "Products", "description": "Manage your product catalog", "adminDescription": "Manage and track user products", "totalProducts": "Total Products", "manage": "Manage Products", "name": "Product Name", "tags": "Tags", "image": "Image", "priceType": {"title": "Price Type", "hasPrice": "Fixed Price", "stringPrice": "Descriptive Price", "noPrice": "No Price"}, "productType": {"title": "Product Type", "physical": "Physical", "digital": "Digital", "service": "Service", "event": "Event", "combo": "Combo"}, "types": {"physical": {"title": "Physical Product", "description": "Tangible products that require shipping"}, "digital": {"title": "Digital Product", "description": "Files, courses, ebooks, software"}, "service": {"title": "Service", "description": "Consulting, beauty, maintenance, installation"}, "event": {"title": "Event", "description": "Workshops, courses, performances"}, "combo": {"title": "Combo", "description": "Product bundle combining multiple types"}}, "typeSelector": {"title": "Select Product Type"}, "status": {"active": "Active", "inactive": "Inactive", "outOfStock": "Out of Stock", "draft": "Draft"}, "form": {"title": "Add New Product", "name": "Product Name", "description": "Description", "price": "Price", "category": "Category", "sku": "SKU", "status": "Status", "submit": "Save Product", "cancel": "Cancel", "createTitle": "Add Physical Product", "createDigitalTitle": "Create Digital Product", "createServiceTitle": "Create Service", "createEventTitle": "Create Event", "createComboTitle": "Create Combo Product", "editTitle": "Edit Product", "updating": "Updating...", "namePlaceholder": "Enter product name", "serviceNamePlaceholder": "Enter service name", "eventNamePlaceholder": "Enter event name", "comboNamePlaceholder": "Enter combo product name", "descriptionPlaceholder": "Enter product description", "serviceDescriptionPlaceholder": "Detailed description of the service", "eventDescriptionPlaceholder": "Detailed description of the event", "comboDescriptionPlaceholder": "Detailed description of the combo product", "tagsPlaceholder": "Enter product tags and press Enter", "media": "Product Images", "mediaPlaceholder": "Drag and drop or click to upload product images", "priceDescriptionPlaceholder": "Enter price description", "listPricePlaceholder": "Enter list price", "salePricePlaceholder": "Enter sale price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "listPrice": "List Price", "salePrice": "Sale Price", "sections": {"generalInfo": "1. General Information", "pricing": "2. Product Pricing", "images": "3. Product Images", "shipping": "5. Shipping", "inventory": "4. Inventory Management", "variants": "6. <PERSON><PERSON><PERSON>", "customFields": "7. Custom Fields", "digitalProcessing": "4. Digital Order Processing", "digitalOutput": "5. Digital Product Output", "media": "3. Product Images", "serviceInfo": "3. Service Information", "eventInfo": "2. Event Organization Information", "ticketTypes": "3. Event Ticket Types", "comboProducts": "2. Products in Combo"}, "shipmentConfig": {"widthCm": "Width (cm)", "heightCm": "Height (cm)", "lengthCm": "Length (cm)", "weightGram": "Weight (gram)"}, "inventory": {"warehouse": "Warehouse", "warehousePlaceholder": "Select warehouse", "availableQuantity": "Available Quantity", "sku": "SKU", "barcode": "Barcode"}, "customFields": {"title": "Custom Fields", "selectField": "Select Custom Field", "selectGroupForm": "Select Custom Field Group", "searchPlaceholder": "Search custom fields...", "searchGroupPlaceholder": "Search custom field groups...", "selectedFields": "Selected Custom Fields", "selectedGroupForm": "Selected Custom Field Group", "addField": "Add Custom Field", "addGroupForm": "Add Custom Field Group", "valuePlaceholder": "Enter value", "selectBoolean": "Select True/False", "noOptions": "No options configured", "validation": {"required": "This field is required", "minLength": "Minimum {{min}} characters", "maxLength": "Maximum {{max}} characters", "pattern": "Invalid format", "invalidNumber": "Must be a valid number", "min": "Minimum {{min}}", "max": "Maximum {{max}}", "invalidEmail": "Invalid email", "invalidUrl": "Invalid URL", "invalidDate": "Invalid date"}}, "variants": {"title": "Product Classification", "addVariant": "Add Classification", "variant": "Classification", "noVariants": "No classifications yet. Click \"Add Classification\" to get started.", "customFields": "Classification Attributes", "searchCustomField": "Search attributes", "description": "Variant Description", "descriptionPlaceholder": "Enter description for this variant", "priceDescription": "Price Description", "priceDescriptionPlaceholder": "Enter price description for this variant"}, "versions": {"title": "Versions", "addVersion": "Add Version", "version": "Version", "noVersions": "No versions yet. Click \"Add Version\" to get started.", "name": "Version Name", "namePlaceholder": "Basic, Pro, Premium...", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "description": "Version Description", "descriptionPlaceholder": "Detailed description of this version...", "quantity": "Available Quantity", "sku": "SKU Code", "skuPlaceholder": "BASIC-001", "minQuantity": "Minimum Quantity Per Purchase", "maxQuantity": "Maximum Quantity Per Purchase", "status": "Status", "statusPending": "Pending", "statusActive": "Active", "statusInactive": "Inactive", "removeVersion": "Remove Version"}, "digitalProduct": {"deliveryMethod": {"title": "Delivery Method", "email": "Email", "dashboardDownload": "Dashboard Download", "sms": "SMS", "directMessage": "Direct Message", "zalo": "<PERSON><PERSON>", "courseActivation": "Course Activation"}, "deliveryTiming": {"title": "Delivery Timing", "immediate": "Immediate after payment", "delayed": "Delayed delivery"}, "deliveryDelayMinutes": "Delay Time (minutes)", "deliveryDelayPlaceholder": "Enter delay time in minutes", "accessStatus": {"title": "Access Status", "pending": "Pending", "delivered": "Delivered", "notDelivered": "Not Delivered", "deliveryError": "Delivery Error"}, "digitalProductType": {"title": "Digital Product Type", "onlineCourse": "Online Course", "fileDownload": "File Download", "licenseKey": "License Key", "ebook": "E-book"}, "downloadLink": "Access Link", "downloadLinkPlaceholder": "Enter digital product access link", "accessLink": "Access Link", "accessLinkPlaceholder": "Enter digital product access link", "loginInfo": {"title": "Course Login Information", "username": "Username", "usernamePlaceholder": "Enter username", "password": "Password", "passwordPlaceholder": "Enter password"}, "usageInstructions": "Usage Instructions", "usageInstructionsPlaceholder": "Enter detailed instructions for using the digital product"}, "serviceProduct": {"serviceTime": "Service Time", "serviceTimePlaceholder": "Select date and time for service execution", "serviceDuration": "Duration (minutes)", "serviceDurationPlaceholder": "Enter service duration", "serviceProvider": "Service Provider", "serviceProviderPlaceholder": "Name of person/team providing the service", "serviceType": {"title": "Service Type", "consultation": "Consultation", "beauty": "Beauty", "maintenance": "Maintenance", "installation": "Installation"}, "serviceLocation": {"title": "Service Location", "atHome": "At Home", "atCenter": "At Center", "online": "Online"}, "defaultPackageName": "Basic Consultation Package", "defaultPackageDescription": "Basic consultation package includes 3 online consultation sessions"}, "eventProduct": {"eventDateTime": "Event Date & Time", "eventDateTimePlaceholder": "Select event date and time", "attendanceMode": {"title": "Attendance Mode", "offline": "Offline (In-person)", "online": "Online (Virtual)"}, "eventLocation": "Event Location", "eventLocationPlaceholder": "Enter event venue location", "zoomLink": "Zoom Link", "zoomLinkPlaceholder": "Enter Zoom link for online event", "ticketTypes": {"title": "Ticket Types List", "addTicket": "Add Ticket Type", "removeTicket": "Remove Ticket Type", "ticketName": "Ticket Name", "ticketNamePlaceholder": "VIP, Regular, Student...", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "totalTickets": "Total Tickets", "sku": "SKU Code", "skuPlaceholder": "VIP-001", "saleStartTime": "Sale Start Time", "saleStartTimePlaceholder": "Select sale start date and time", "saleEndTime": "Sale End Time", "saleEndTimePlaceholder": "Select sale end date and time", "minQuantityPerOrder": "Min Tickets per Order", "maxQuantityPerOrder": "Max Tickets per Order", "ticketImage": "Ticket Image", "ticketImagePlaceholder": "No ticket image", "description": "Ticket Description", "descriptionPlaceholder": "Detailed description of this ticket type...", "defaultTicketName": "Ticket Type", "defaultTicketDescription": "Basic conference participation ticket"}, "defaultEventLocation": "National Convention Center, Hanoi"}, "comboProduct": {"searchProductLabel": "Search products to add to combo:", "selectedProductsLabel": "Selected Products", "noProductsMessage": "No products in combo yet. Please select products above.", "productAlreadyExists": "This product is already in the combo", "tableHeaders": {"productName": "Product Name", "quantity": "Quantity", "originalPrice": "Original Price", "actions": "Actions"}, "totalLabel": "Total:", "listPriceNote": "Automatically calculated from total product prices in combo", "salePriceNote": "Sale price is usually lower than list price to create discount", "priceDescriptionPlaceholder": "Example: Contact for combo pricing"}, "validation": {"nameRequired": "Product name is required", "priceTypeRequired": "Please select a price type", "listPriceRequired": "Please enter list price", "salePriceRequired": "Please enter sale price", "currencyRequired": "Please select currency", "priceDescriptionRequired": "Please enter price description", "loginInfoRequired": "Please enter login information for online course", "versionNameRequired": "Version name is required", "versionPriceMin": "Version price must be >= 0", "versionCurrencyRequired": "Currency is required", "versionQuantityMin": "Quantity must be >= 1", "versionMinQuantityMin": "Minimum quantity must be >= 1", "versionMaxQuantityMin": "Maximum quantity must be >= 1", "listPriceInvalid": "List price must be a number > 0", "salePriceInvalid": "Sale price must be a number > 0", "comboTotalPriceInvalid": "Total value of products in combo must be greater than 0", "listPriceGreaterThanSale": "List price must be greater than sale price", "invalidPriceType": "Invalid price type", "formValidationError": "Please check the information entered", "requiredFieldsMissing": "Please enter product name and select price type", "serviceRequiredFieldsMissing": "Please enter service name and select price type", "priceValidationError": "Price validation error", "serviceDurationMin": "Service duration must be greater than 0", "serviceNameRequired": "Service name is required", "eventNameRequired": "Event name is required", "eventRequiredFieldsMissing": "Please enter event name", "eventLocationRequired": "Please enter event location for offline events", "zoomLinkInvalid": "Invalid Zoom link", "ticketTypesRequired": "Must have at least 1 ticket type", "ticketNameRequired": "Ticket name is required", "ticketPriceMin": "Ticket price must be >= 0", "ticketTotalMin": "Total tickets must be >= 1", "ticketMinQuantityMin": "Minimum tickets must be >= 1", "ticketMaxQuantityMin": "Maximum tickets must be >= 1", "ticketMaxGreaterThanMin": "Maximum tickets must be >= minimum tickets", "ticketUploadSuccess": "Ticket images uploaded successfully", "ticketUploadError": "Error uploading ticket images", "comboNameRequired": "Combo name is required", "comboRequiredFieldsMissing": "Please enter combo name and select price type", "comboProductsRequired": "Combo must have at least 1 product", "comboProductIdInvalid": "Invalid product ID", "comboProductNameRequired": "Product name is required", "comboProductQuantityMin": "Quantity must be greater than 0"}}, "customFields": {"title": "Custom Fields", "selectField": "Select Custom Field", "selectGroupForm": "Select Custom Field Group", "searchPlaceholder": "Search custom fields...", "searchGroupPlaceholder": "Search custom field groups...", "selectedFields": "Selected Custom Fields", "selectedGroupForm": "Selected Custom Field Group", "addField": "Add Custom Field", "addGroupForm": "Add Custom Field Group"}, "listPrice": "List Price", "enterListPrice": "Enter list price", "enterSalePrice": "Enter sale price", "salePrice": "Sale Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "priceDescription": "Price Description", "createSuccess": "Product created successfully", "createError": "Error creating product", "updateSuccess": "Product updated successfully", "updateError": "Error updating product", "deleteSuccess": "Product deleted successfully", "deleteError": "Error deleting product", "bulkDeleteSuccess": "{{count}} products deleted successfully", "bulkDeleteError": "Error deleting multiple products", "selectToDelete": "Please select at least one product to delete", "confirmDeleteMessage": "Are you sure you want to delete this product?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected products?", "createProduct": "Create Product", "editProduct": "Edit Product", "productList": "Product List", "productDetails": "Product Details", "productInfo": "Product Information", "productAttributes": "Product Attributes", "productImages": "Product Images", "fields": {"name": "Product Name", "price": "Price", "priceType": "Price Type", "priceTypes": {"yes": "Yes", "no": "No", "other": "Other"}, "regularPrice": "Regular Price", "salePrice": "Sale Price", "priceNote": "Price Note", "brand": "Brand", "url": "URL", "description": "Description", "attributes": "Attributes", "attributeName": "Attribute Name", "attributeType": "Data Type", "attributeValue": "Default Value"}, "attributeTypes": {"text": "Text", "number": "Number", "date": "Date", "boolean": "Yes/No", "list": "List"}, "images": {"addImages": "Add Product Images", "image": "Image", "url": "URL", "video": "Video", "uploadImage": "Upload Image", "enterImageUrl": "Enter Image URL", "enterVideoUrl": "Enter Video URL", "recommendedSize": "Recommended size: 800x600px, max 2MB", "addToList": "Add to List", "uploadedImages": "Uploaded Images", "urlImages": "URL Images", "videoList": "Video List", "setCover": "Set as Cover", "coverImage": "Cover Image", "uploadedFromComputer": "Uploaded from computer", "dragAndDrop": "Drag and drop or click to upload product image"}, "actions": {"createProduct": "Create Product", "saveProduct": "Save Product", "deleteProduct": "Delete Product", "cancelCreation": "Cancel"}, "messages": {"productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "productDeleted": "Product deleted successfully", "confirmDelete": "Are you sure you want to delete this product?"}, "import": {"title": "Import Products", "steps": {"upload": "Upload File", "mapping": "Column Mapping", "preview": "Preview Data", "importing": "Importing"}, "mapping": {"title": "Column Mapping", "description": "Map Excel columns to product fields", "columnMapping": "Column Mapping", "skipColumn": "Skip this column", "requiredField": "This field is required", "dataPreview": "Data Preview", "validationErrors": "Validation Errors", "errors": {"duplicateMapping": "This field is already mapped to another column", "requiredFieldMissing": "Required field {{field}} is not mapped"}}, "upload": {"title": "Upload Product File", "description": "Select Excel file or enter URL to import product list", "fromFile": "From File", "fromUrl": "From URL", "supportedFormats": "Supported: .xlsx, .xls, .csv (max 10MB)", "hasHeader": "File has header row", "excelUrl": "Excel file URL", "urlPlaceholder": "Enter Excel file URL...", "loading": "Loading...", "loadFromUrl": "Load from URL"}, "errors": {"parseError": "Error parsing file", "urlRequired": "URL is required", "urlFetchError": "Error fetching file from URL", "urlLoadError": "Error loading file from URL"}, "progress": {"importing": "Importing Products", "pleaseWait": "Please wait while the system processes the data", "processing": "Processing", "imported": "Imported", "errors": "Errors"}, "complete": {"title": "Import Complete", "description": "Product import has been completed successfully", "totalProcessed": "Total Processed", "successfullyImported": "Successfully Imported", "failed": "Failed", "errorDetails": "<PERSON><PERSON><PERSON>", "nextSteps": "Next Steps", "reviewProducts": "Review product list", "updateInventory": "Update inventory levels", "setupCategories": "Setup product categories", "viewProducts": "View Products"}, "preview": {"title": "Preview Import Data", "description": "Review and validate data before importing", "totalRows": "Total Rows", "validRows": "Valid <PERSON>s", "invalidRows": "Invalid Rows", "validationWarnings": "Validation Warnings", "dataPreview": "Data Preview", "showingFirst10": "Showing first 10 rows", "importOptions": "Import Options", "skipInvalidRows": "Skip invalid rows", "updateExisting": "Update existing products", "sendNotification": "Send notification", "startImport": "Start Import", "row": "Row"}, "validation": {"nameRequired": "Product name is required", "skuRequired": "SKU is required", "priceRequired": "Price is required", "invalidPrice": "Price must be a positive number", "invalidStock": "Stock must be a non-negative number"}}}, "customField": {"configId": "Field Identifier Name", "title": "Custom Fields", "description": "Manage custom fields", "adminDescription": "Manage system custom fields", "add": "Add Custom Field", "edit": "Edit Custom Field", "addForm": "Add New Custom Field", "editForm": "Edit Custom Field", "component": "Component Type", "components": {"input": "Input", "textarea": "Textarea", "select": "Select", "checkbox": "Checkbox", "radio": "Radio", "date": "Date", "number": "Number", "file": "File", "multiSelect": "Multi-select"}, "type": "Data Type", "type.string": "Text", "type.number": "Number", "type.boolean": "Boolean", "type.date": "Date", "type.object": "Object", "type.array": "Array", "types": {"text": "Text", "number": "Number", "boolean": "Boolean", "date": "Date", "select": "Select Box", "object": "Object", "array": "Array"}, "name": "Field Name", "label": "Label", "placeholder": "Placeholder", "defaultValue": "Default Value", "options": "Options", "required": "Required", "validation": {"minLength": "Min Length", "maxLength": "Max Length", "pattern": "Pattern", "min": "Min Value", "max": "Max Value"}, "form": {"showAdvancedSettings": "Show Advanced Settings", "componentRequired": "Please select a component type", "labelRequired": "Please enter a label", "typeRequired": "Please select a data type", "idRequired": "Please enter field identifier name", "labelPlaceholder": "Enter display label", "descriptionPlaceholder": "Enter field description", "placeholderPlaceholder": "Enter placeholder text", "defaultValuePlaceholder": "Enter default value", "optionsPlaceholder": "Enter options, comma separated or JSON format", "selectOptionsPlaceholder": "Enter values in Name|Value format, one pair per line. Example:\na|1\nb|2", "booleanDefaultPlaceholder": "Select default value", "dateDefaultPlaceholder": "Select default date", "labelTagRequired": "Please add at least one label", "fieldIdLabel": "Field Identifier Name", "fieldIdPlaceholder": "text-input-001", "displayNameLabel": "Display Name", "displayNamePlaceholder": "Enter display name for this field", "description": "Description", "displayNameRequired": "Please enter display name", "labelInputPlaceholder": "Enter label and press Enter", "tagsCount": "labels added", "patternSuggestions": "Common pattern suggestions:", "defaultValue": "Default Value", "minLength": "Min Length", "maxLength": "Max Length", "pattern": "Pattern", "options": "Options", "min": "Min Value", "max": "Max Value", "placeholder": "Placeholder", "required": "Required", "label": "Label"}, "createSuccess": "Custom field created successfully", "createError": "Error creating custom field", "updateSuccess": "Custom field updated successfully", "updateError": "Error updating custom field", "deleteSuccess": "Custom field deleted successfully", "deleteError": "Error deleting custom field", "loadError": "Error loading custom fields", "booleanValues": {"true": "Yes", "false": "No"}, "patterns": {"email": "Email", "phoneVN": "VN Phone Number", "phoneIntl": "International Phone", "postalCodeVN": "VN Postal Code", "lettersOnly": "Letters Only", "numbersOnly": "Numbers Only", "alphanumeric": "Letters & Numbers", "noSpecialChars": "No Special Characters", "url": "URL", "ipv4": "IPv4", "strongPassword": "Strong Password", "vietnameseName": "Vietnamese Name", "studentId": "Student ID", "nationalId": "National ID", "taxCode": "Tax Code", "dateFormat": "Date (dd/mm/yyyy)", "timeFormat": "Time (hh:mm)", "hexColor": "Hex Color", "base64": "Base64", "uuid": "UUID", "filename": "Filename", "urlSlug": "URL Slug", "variableName": "Variable Name", "creditCard": "Credit Card Number", "qrCode": "QR Code", "gpsCoordinate": "GPS Coordinate", "rgbColor": "RGB Color", "domain": "Domain Name", "decimal": "Decimal Number", "barcode": "Barcode"}, "confirmDeleteMessage": "Are you sure you want to delete this custom field?", "bulkDeleteSuccess": "Successfully deleted {{count}} custom fields", "bulkDeleteError": "Error occurred while deleting custom fields", "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected custom fields?", "selectedItems": "{{count}} items selected"}, "customGroupForm": {"title": "Custom Field Groups", "description": "Manage custom field groups", "createSuccess": "Custom field group created successfully", "createError": "Error creating custom field group", "updateSuccess": "Custom field group updated successfully", "updateError": "Error updating custom field group", "deleteSuccess": "Custom field group deleted successfully", "deleteError": "Error deleting custom field group", "loadError": "Error loading custom field groups"}, "order": {"notesPlaceholder": "Enter notes for this order", "title": "Orders", "description": "Manage orders", "adminDescription": "Manage and track user orders", "createOrder": "Create New Order", "editOrder": "Edit Order", "viewOrder": "View Order Details", "orderNumber": "Order Number", "customerInfo": "Customer Information", "customerName": "Customer Name", "customerEmail": "Email", "customerPhone": "Phone", "customerAddress": "Address", "items": "Order Items", "noItems": "No items in this order", "quantity": "Quantity", "totalAmount": "Total Amount", "status": {"title": "Order Status", "pending": "Pending", "confirmed": "Confirmed", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled"}, "shippingStatus": {"title": "Shipping Status", "pending": "Pending", "preparing": "Preparing", "shipped": "Shipped", "inTransit": "In Transit", "sorting": "Sorting", "delivered": "Delivered", "deliveryFailed": "Delivery Failed", "returning": "Returning", "cancelled": "Cancelled"}, "paymentMethod": "Payment Method", "paymentMethods": {"cash": "Cash", "creditCard": "Credit Card", "bankTransfer": "Bank Transfer", "digitalWallet": "Digital Wallet"}, "paymentStatus": {"title": "Payment Status", "paid": "Paid", "pending": "Pending", "unpaid": "Unpaid", "failed": "Failed", "refunded": "Refunded", "partiallyPaid": "Partially Paid"}, "notes": "Notes", "shippingMethod": "Shipping Method", "shippingFee": "Shipping Fee", "shippingNote": "Shipping Note", "shippingNotePlaceholder": "Enter shipping note...", "codAmount": "COD Amount", "tags": "Order Tags", "tagsPlaceholder": "Enter tag and press Enter", "addTag": "Add Tag", "removeTag": "Remove Tag", "subtotal": "Subtotal", "form": {"customerNamePlaceholder": "Enter customer name", "customerEmailPlaceholder": "Enter customer email", "customerPhonePlaceholder": "Enter customer phone", "customerAddressPlaceholder": "Enter customer address", "notesPlaceholder": "Enter notes for this order"}, "createSuccess": "Order created successfully", "createError": "Error creating order", "updateSuccess": "Order updated successfully", "updateError": "Error updating order", "deleteSuccess": "Order deleted successfully", "deleteError": "Error deleting order", "confirmDeleteMessage": "Are you sure you want to delete this order?", "estimatedDelivery": "Estimated Delivery", "tax": "Tax", "printInProgress": "Preparing to print order...", "tracking": "Order Tracking", "carrier": "Carrier", "trackingNumber": "Tracking Number", "currentStatus": "Current Status", "loadingTracking": "Loading tracking information...", "trackingError": "Error loading tracking information", "trackingErrorGeneric": "An error occurred while loading tracking information", "trackingTimeline": "Shipping Timeline", "detailedTracking": "Detailed Tracking Information", "noTrackingInfo": "No tracking information available", "printSuccess": "Print order successful", "printSuccessDescription": "Order has been prepared for printing", "printError": "Print order error", "printErrorDescription": "Unable to print order. Please try again.", "batchPrintSuccess": "Batch print successful", "batchPrintSuccessDescription": "Successfully printed {{count}} orders", "batchPrintError": "Batch print error", "batchPrintErrorDescription": "Unable to batch print orders. Please try again.", "fetchPrintableOrdersError": "Unable to load printable orders list", "notFound": "Order not found", "notFoundDescription": "The order does not exist or you do not have permission to view it.", "backToList": "Back to list", "loading": "Loading order details...", "createdAt": "Created at", "productInfo": "Product Information", "total": "Total", "recipient": "Recipient", "phone": "Phone", "print": "Print Order", "deliveryConfiguration": "Delivery Configuration", "products": "products", "physicalShippingTitle": "Physical Product Shipping", "digitalDeliveryTitle": "Digital Product Delivery", "serviceDeliveryTitle": "Service Information", "eventDeliveryTitle": "Event Information", "selectProductsFirstDescription": "Please select products to configure delivery", "deliveryAddress": "Delivery Address", "enhancedOrderForm": {"title": "Create New Order", "editTitle": "Edit Order", "stepTitles": {"customer": "Customer Information", "products": "Select Products", "delivery": "Delivery Configuration", "payment": "Payment Information", "review": "Review Order"}, "navigation": {"previous": "Previous", "next": "Next", "cancel": "Cancel", "submit": "Create Order", "update": "Update Order"}, "validation": {"customerRequired": "Please select a customer", "productsRequired": "Please select at least one product", "paymentMethodRequired": "Please select a payment method"}, "summary": {"title": "Order Summary", "customer": "Customer", "products": "Products", "subtotal": "Subtotal", "shipping": "Shipping Fee", "total": "Total", "paymentMethod": "Payment Method", "notes": "Notes"}}}, "conversion": {"title": "Conversions", "description": "Track and manage conversions", "adminDescription": "Track and manage conversion records", "totalConversions": "Total Conversions", "manage": "Manage Conversions", "id": "ID", "customerId": "Customer ID", "userId": "User ID", "type": "Conversion Type", "name": "Name", "source": "Source", "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed"}}, "warehouseCustomField": {"title": "Warehouse Custom Fields", "description": "Manage warehouse custom fields"}, "file": {"title": "File Management", "description": "Manage files and documents"}, "folder": {"title": "Folder Management", "description": "Manage folder structure"}, "report": {"title": "Reports", "description": "View business reports", "totalReports": "Total Reports", "view": "View Reports", "loading": "Loading...", "noData": "No data available", "totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "newCustomers": "New Customers", "topSellingProducts": "Top Selling Products", "potentialCustomers": "Potential Customers", "tabs": {"sales": "Sales", "orders": "Orders", "customers": "Customers", "products": "Products"}, "charts": {"salesPlaceholder": "Sales chart will be displayed here", "ordersPlaceholder": "Orders chart will be displayed here", "customersPlaceholder": "Customers chart will be displayed here", "productsPlaceholder": "Products chart will be displayed here", "labels": {"revenue": "Revenue", "newCustomers": "New Customers", "totalCustomers": "Total Customers", "totalProducts": "Total Products", "newProducts": "New Products", "soldProducts": "Sold Products", "timeLabel": "Time"}}}, "inventory": {"title": "Inventory", "description": "Manage inventory and stock", "totalItems": "Total Items", "totalProducts": "Total Products", "manage": "Manage Inventory", "currentQuantity": "Current Quantity", "availableQuantity": "Available Quantity", "reservedQuantity": "Reserved Quantity", "defectiveQuantity": "Defective Quantity", "totalQuantity": "Total Quantity", "updateQuantity": "Update Quantity", "addProduct": "Add Product", "noProducts": "No products in warehouse", "noProductsDescription": "This warehouse currently has no products. Add products to the warehouse to start managing.", "status": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "warehouseProducts": "Warehouse Products", "noWarehouseProducts": "No products in warehouse", "noWarehouseProductsDescription": "This warehouse currently has no products. Add products to the warehouse to start managing.", "confirmBulkDeleteTitle": "Confirm Delete Inventory", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected inventory items? This action cannot be undone.", "bulkDeleteSuccess": "Successfully deleted {{count}} inventory items", "bulkDeleteError": "Error deleting inventory items", "selectToDelete": "Please select at least one item to delete"}, "warehouse": {"title": "Warehouses", "description": "Manage warehouses", "adminDescription": "Manage physical and virtual warehouses for users", "viewDetail": "View Details", "name": "Warehouse Name", "code": "Warehouse Code", "desc": "Description", "type": "Warehouse Type", "types": {"PHYSICAL": "Physical Warehouse", "VIRTUAL": "Virtual Warehouse"}, "status": "Status", "address": "Address", "contact": "Contact Information", "add": "Add Warehouse", "edit": "Edit Warehouse", "addForm": "Add New Warehouse", "editForm": "Edit Warehouse Information", "createSuccess": "Warehouse created successfully", "updateSuccess": "Warehouse updated successfully", "deleteSuccess": "Warehouse deleted successfully", "createError": "Error creating warehouse", "updateError": "Error updating warehouse", "deleteError": "Error deleting warehouse", "confirmDeleteMessage": "Are you sure you want to delete this warehouse?", "notFound": "Warehouse not found", "form": {"namePlaceholder": "Enter warehouse name", "descriptionPlaceholder": "Enter warehouse description", "typePlaceholder": "Select warehouse type", "selectType": "Select warehouse type"}}, "physicalWarehouse": {"title": "Physical Warehouses", "information": "Manage physical warehouses", "description": "Manage physical warehouses", "manage": "Manage Physical Warehouses", "totalWarehouses": "total physical warehouses", "name": "Physical Warehouse Name", "warehouse": "Warehouse", "address": "Address", "capacity": "Capacity", "actions": "Actions", "add": "Add Physical Warehouse", "create": "Create Physical Warehouse", "edit": "Edit", "delete": "Delete", "view": "View Details", "type": "Type Warehouse", "search": "Search physical warehouses...", "noData": "No physical warehouse data", "createSuccess": "Physical warehouse created successfully", "updateSuccess": "Physical warehouse updated successfully", "deleteSuccess": "Physical warehouse deleted successfully", "deleteMultipleSuccess": "Multiple physical warehouses deleted successfully", "createError": "Failed to create physical warehouse", "updateError": "Failed to update physical warehouse", "deleteError": "Failed to delete physical warehouse", "deleteMultipleError": "Failed to delete multiple physical warehouses", "errors": {"nameRequired": "Warehouse name is required", "nameExists": "Warehouse name already exists"}, "selectToDelete": "Please select at least one physical warehouse to delete", "confirmDeleteMessage": "Are you sure you want to delete this physical warehouse?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected physical warehouses?", "form": {"createTitle": "Create New Physical Warehouse", "editTitle": "Edit Physical Warehouse", "create": "Create Physical Warehouse", "update": "Update", "selectWarehouse": "Select Warehouse", "warehousePlaceholder": "Select warehouse to create physical warehouse", "warehouseRequired": "Warehouse is required", "addressPlaceholder": "Enter physical warehouse address", "addressRequired": "Address is required", "addressMaxLength": "Address cannot exceed 255 characters", "capacityPlaceholder": "Enter warehouse capacity", "capacityMin": "Capacity must be greater than or equal to 0"}}, "virtualWarehouse": {"title": "Virtual Warehouses", "description": "Manage virtual warehouses and digital storage systems", "manage": "Manage Virtual Warehouses", "totalWarehouses": "total virtual warehouses", "name": "Virtual Warehouse Name", "status": {"title": "Status", "active": "Active", "inactive": "Inactive"}, "associatedSystem": "Associated System", "purpose": "Purpose", "actions": "Actions", "create": "Create Virtual Warehouse", "edit": "Edit Virtual Warehouse", "delete": "Delete Virtual Warehouse", "view": "View Details", "search": "Search virtual warehouses...", "noData": "No virtual warehouse data", "createSuccess": "Virtual warehouse created successfully", "updateSuccess": "Virtual warehouse updated successfully", "deleteSuccess": "Virtual warehouse deleted successfully", "deleteMultipleSuccess": "Multiple virtual warehouses deleted successfully", "createError": "Failed to create virtual warehouse", "updateError": "Failed to update virtual warehouse", "deleteError": "Failed to delete virtual warehouse", "deleteMultipleError": "Failed to delete multiple virtual warehouses", "confirmDeleteMessage": "Are you sure you want to delete this virtual warehouse?", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected virtual warehouses?", "form": {"createTitle": "Create New Virtual Warehouse", "editTitle": "Edit Virtual Warehouse", "create": "Create Virtual Warehouse", "update": "Update", "warehousePlaceholder": "Select warehouse to create virtual warehouse", "warehouseRequired": "Warehouse is required", "descriptionPlaceholder": "Enter virtual warehouse description", "descriptionMaxLength": "Description cannot exceed 500 characters", "associatedSystemPlaceholder": "Enter associated system", "associatedSystemMaxLength": "Associated system cannot exceed 200 characters", "purposePlaceholder": "Enter purpose", "purposeMaxLength": "Purpose cannot exceed 300 characters", "statusPlaceholder": "Select status", "statusRequired": "Status is required"}}, "shop": {"title": "Shop Management", "description": "Manage shop information and shipping units", "form": {"shopId": "Shop ID", "shopName": "Shop Name", "shopNamePlaceholder": "Enter shop name", "shopPhone": "Phone Number", "shopPhonePlaceholder": "Enter shop phone number", "shopAddress": "Address", "shopAddressPlaceholder": "Enter shop address", "shopProvince": "Province/City", "shopProvincePlaceholder": "Enter province/city", "shopDistrict": "District", "shopDistrictPlaceholder": "Enter district", "shopWard": "Ward", "shopWardPlaceholder": "Enter ward", "createTitle": "Create Shop Information", "editTitle": "Update Shop Information", "description": "Enter shop information to manage orders and shipping"}, "empty": {"title": "No shops yet", "description": "Create your first shop to start managing orders and shipping", "action": "Create Shop"}, "messages": {"createSuccess": "Shop created successfully", "createError": "Error occurred while creating shop", "updateSuccess": "Shop updated successfully", "updateError": "Error occurred while updating shop", "loadError": "Error loading shop information", "noShopInfo": "No shop information available. Please create shop information to manage orders.", "bulkDeleteSuccess": "Successfully deleted {{count}} shops", "bulkDeleteError": "Error occurred while deleting multiple shops"}, "selectToDelete": "Please select at least one shop to delete", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected shops?"}}}