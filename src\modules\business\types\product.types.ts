/**
 * <PERSON><PERSON><PERSON> ngh<PERSON> cá<PERSON> types cho Product trong module business
 */

/**
 * Enum cho loại sản phẩm
 */
export enum ProductTypeEnum {
  PHYSICAL = 'PHYSICAL',
  DIGITAL = 'DIGITAL',
  SERVICE = 'SERVICE',
  EVENT = 'EVENT',
  COMBO = 'COMBO',
}

/**
 * Enum cho loại giá sản phẩm
 */
export enum PriceTypeEnum {
  /**
   * Có giá cố định
   */
  HAS_PRICE = 'HAS_PRICE',

  /**
   * Gi<PERSON> dạng chuỗi(mô tả)
   */
  STRING_PRICE = 'STRING_PRICE',

  /**
   * Không có giá
   */
  NO_PRICE = 'NO_PRICE',
}

/**
 * Interface cho giá sản phẩm khi typePrice là HAS_PRICE
 */
export interface HasPriceDto {
  /**
   * Giá niêm yết
   */
  listPrice: number;

  /**
   * G<PERSON><PERSON> bán
   */
  salePrice: number;

  /**
   * Đơn vị tiền tệ
   */
  currency: string;
}

/**
 * Interface cho giá sản phẩm khi typePrice là STRING_PRICE
 */
export interface StringPriceDto {
  /**
   * Mô tả giá
   */
  priceDescription: string;
}

/**
 * Interface cho giá trong classification (hỗ trợ cả HAS_PRICE và STRING_PRICE)
 */
export interface ClassificationPriceDto {
  /**
   * Giá niêm yết (cho HAS_PRICE)
   */
  listPrice?: number;

  /**
   * Giá bán (cho HAS_PRICE)
   */
  salePrice?: number;

  /**
   * Đơn vị tiền tệ (cho HAS_PRICE)
   */
  currency?: string;

  /**
   * Mô tả giá (cho STRING_PRICE)
   */
  priceDescription?: string;
}

/**
 * Interface cho thông tin hình ảnh sản phẩm
 */
export interface ProductImageDto {
  /**
   * Key của hình ảnh trên S3
   */
  key: string;

  /**
   * Vị trí của hình ảnh
   */
  position: number;

  /**
   * URL để xem hình ảnh
   */
  url: string;
}

/**
 * Interface cho cấu hình vận chuyển
 */
export interface ShipmentConfigDto {
  /**
   * Chiều dài (cm)
   */
  lengthCm?: number;

  /**
   * Chiều rộng (cm)
   */
  widthCm?: number;

  /**
   * Chiều cao (cm)
   */
  heightCm?: number;

  /**
   * Cân nặng (gram)
   */
  weightGram?: number;
}

/**
 * Interface cho phân loại sản phẩm
 */
export interface ClassificationDto {
  /**
   * ID của phân loại
   */
  id: number;

  /**
   * Loại phân loại
   */
  type: string;

  /**
   * Mô tả phân loại
   */
  description?: string;

  /**
   * Mô tả giá cho phân loại (khi typePrice = STRING_PRICE)
   */
  priceDescription?: string;

  /**
   * Giá của phân loại (hỗ trợ cả HAS_PRICE và STRING_PRICE)
   */
  price: ClassificationPriceDto;

  /**
   * Danh sách custom fields của phân loại (theo cấu trúc API thực tế)
   */
  customFields: Array<{
    customFieldId: number;
    value: Record<string, unknown>;
  }>;

  /**
   * Danh sách hình ảnh của phân loại
   */
  images?: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

/**
 * Interface cho metadata chứa custom fields
 */
export interface ProductMetadataDto {
  /**
   * Danh sách custom fields
   */
  customFields?: Array<{
    id: number;
    tags: string[];
    type: string;
    label: string;
    value: Record<string, unknown>;
    configId: string;
    required: boolean;
    configJson: Record<string, unknown>;
  }>;
}

/**
 * Interface cho thông tin sản phẩm
 */
export interface ProductDto {
  /**
   * ID của sản phẩm
   */
  id: number;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Loại sản phẩm
   */
  productType?: ProductTypeEnum;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách hình ảnh
   */
  images?: ProductImageDto[];

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * ID người tạo
   */
  createdBy: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updatedAt: number;

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: ShipmentConfigDto;

  /**
   * Metadata chứa custom fields
   */
  metadata?: ProductMetadataDto;

  /**
   * Danh sách phân loại sản phẩm
   */
  classifications?: ClassificationDto[];

  /**
   * Thông tin tồn kho
   */
  inventory?: {
    warehouseId?: number;
    availableQuantity?: number;
    sku?: string;
    barcode?: string;
  };

  /**
   * Thông tin nâng cao cho các loại sản phẩm đặc biệt (theo API structure)
   */
  advancedInfo?: Record<string, unknown>;
}

/**
 * Interface cho tham số truy vấn sản phẩm
 */
export interface ProductQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng sản phẩm trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm theo tên sản phẩm
   */
  search?: string | undefined;

  /**
   * Loại giá
   */
  typePrice?: PriceTypeEnum;

  /**
   * Loại sản phẩm
   */
  productType?: ProductTypeEnum;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string | undefined;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC' | undefined;
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  /**
   * Danh sách items
   */
  items: T[];

  /**
   * Thông tin phân trang
   */
  meta: {
    /**
     * Tổng số items
     */
    totalItems: number;

    /**
     * Số lượng items trên trang hiện tại
     */
    itemCount: number;

    /**
     * Số lượng items trên một trang
     */
    itemsPerPage: number;

    /**
     * Tổng số trang
     */
    totalPages: number;

    /**
     * Trang hiện tại
     */
    currentPage: number;
  };
}

/**
 * Interface cho trường tùy chỉnh
 */
export interface CustomFieldDto {
  /**
   * ID của trường tùy chỉnh
   */
  customFieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   */
  value: Record<string, unknown>;
}

/**
 * Interface cho biến thể sản phẩm (để gửi lên backend)
 */
export interface ProductVariantDto {
  /**
   * ID của phân loại (cho update)
   */
  id?: number;

  /**
   * Loại phân loại
   */
  type: string;

  /**
   * Mô tả biến thể
   */
  description?: string;

  /**
   * Mô tả giá cho biến thể (khi typePrice = STRING_PRICE)
   */
  priceDescription?: string;

  /**
   * Giá của phân loại (hỗ trợ cả HAS_PRICE và STRING_PRICE)
   */
  price?: ClassificationPriceDto;

  /**
   * Danh sách trường tùy chỉnh của phân loại
   */
  customFields?: CustomFieldDto[];

  /**
   * Danh sách thao tác ảnh cho phân loại
   */
  images?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;
}

/**
 * Interface cho nhóm trường tùy chỉnh
 */
export interface CustomGroupFormDto {
  /**
   * ID của nhóm trường tùy chỉnh
   */
  groupId: number;

  /**
   * Danh sách trường tùy chỉnh trong nhóm
   */
  fields: CustomFieldDto[];
}

/**
 * Interface cho tạo sản phẩm mới (đã sync với Backend API)
 */
export interface CreateProductDto {
  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Loại sản phẩm (bắt buộc)
   */
  productType: ProductTypeEnum;

  /**
   * Loại giá
   */
  typePrice: PriceTypeEnum;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách media types của ảnh sản phẩm
   */
  imagesMediaTypes?: string[];

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Danh sách trường tùy chỉnh
   */
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: unknown;
    };
  }>;

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: {
    widthCm?: number;
    heightCm?: number;
    lengthCm?: number;
    weightGram?: number;
  };

  /**
   * Danh sách phân loại sản phẩm
   */
  classifications?: Array<{
    type: string;
    price: {
      listPrice: number;
      salePrice: number;
      currency: string;
    };
    imagesMediaTypes?: string[];
    customFields?: Array<{
      customFieldId: number;
      value: {
        value: unknown;
      };
    }>;
  }>;

  /**
   * Danh sách thông tin tồn kho
   */
  inventory?: Array<{
    warehouseId: number;
    availableQuantity?: number;
    sku?: string;
    barcode?: string;
  }>;

  /**
   * Thông tin nâng cao cho các loại sản phẩm đặc biệt (theo API structure)
   */
  advancedInfo?: DigitalProductAdvancedInfoForCreate | EventProductConfig | ServiceProductConfig | ComboProductConfig;
}

/**
 * Interface cho thông tin nâng cao của sản phẩm trong update
 */
export interface ProductAdvancedInfo {
  /**
   * Số lượng đã mua
   */
  purchaseCount?: number;

  /**
   * Quy trình xử lý đơn hàng số (cho sản phẩm số)
   */
  digitalFulfillmentFlow?: {
    deliveryMethod: 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation';
    deliveryTiming: 'immediate' | 'delayed';
    deliveryDelayMinutes: number;
    accessStatus: 'pending' | 'delivered' | 'not_delivered' | 'delivery_error';
  };

  /**
   * Đầu ra sản phẩm số (cho sản phẩm số)
   */
  digitalOutput?: {
    outputType: 'online_course' | 'file_download' | 'license_key' | 'ebook';
    accessLink?: string;
    loginInfo?: {
      username: string;
      password: string;
    };
    usageInstructions?: string;
  };

  /**
   * Metadata của các biến thể
   */
  variantMetadata?: {
    variants: Array<{
      name: string;
      sku?: string;
      availableQuantity?: number;
      minQuantityPerPurchase?: number;
      maxQuantityPerPurchase?: number;
      price?: {
        listPrice: number;
        salePrice: number;
        currency: string;
      };
      imagesMediaTypes?: string[];
      description?: string;
    }>;
  };
}

/**
 * Interface cho cập nhật sản phẩm
 */
export interface UpdateProductDto {
  /**
   * Tên sản phẩm
   */
  name?: string;

  /**
   * Loại sản phẩm
   */
  productType?: ProductTypeEnum;

  /**
   * Giá sản phẩm
   */
  price?: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice?: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách thao tác ảnh
   */
  images?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: ShipmentConfigDto;

  /**
   * Danh sách custom fields cho sản phẩm
   */
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: unknown;
    };
  }>;

  /**
   * Danh sách phân loại sản phẩm (sử dụng cấu trúc của ProductVariantDto)
   */
  classifications?: ProductVariantDto[];

  /**
   * Thông tin nâng cao cho sản phẩm số, sự kiện, dịch vụ, combo
   */
  advancedInfo?: DigitalProductConfig | EventProductConfig | ServiceProductConfig | ComboProductConfig;
}

/**
 * Interface chuyên biệt cho cập nhật sản phẩm số với đầy đủ thông tin
 */
export interface UpdateDigitalProductDto {
  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Giá sản phẩm (bắt buộc cho sản phẩm số)
   */
  price: HasPriceDto | StringPriceDto;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Danh sách thao tác ảnh
   */
  imageOperations?: Array<{
    operation: 'ADD' | 'DELETE';
    key?: string;
    mimeType?: string;
  }>;

  /**
   * Danh sách custom fields cho sản phẩm
   */
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: unknown;
    };
  }>;

  /**
   * Thông tin nâng cao cho sản phẩm số (bắt buộc)
   */
  advancedInfo: DigitalProductAdvancedInfo;
}

/**
 * Interface cho thông tin nâng cao của sản phẩm số (theo API structure)
 */
export interface DigitalProductAdvancedInfo {
  /**
   * Số lượt mua
   */
  purchaseCount: number;

  /**
   * Quy trình xử lý đơn hàng số
   */
  digitalFulfillmentFlow: {
    deliveryMethod: 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation';
    deliveryTiming: 'immediate' | 'delayed';
    deliveryDelayMinutes: number;
    accessStatus: 'pending' | 'delivered' | 'failed';
  };

  /**
   * Đầu ra sản phẩm số
   */
  digitalOutput: {
    outputType: 'online_course' | 'file_download' | 'license_key' | 'ebook';
    accessLink: string;
    loginInfo?: {
      username: string;
      password: string;
    };
    usageInstructions: string;
  };

  /**
   * Metadata cho variants (cho update)
   */
  variantMetadata?: {
    variants: Array<{
      name: string;
      sku: string;
      availableQuantity: number;
      minQuantityPerPurchase: number;
      maxQuantityPerPurchase: number;
      price: {
        listPrice: number;
        salePrice: number;
        currency: string;
      };
      imageOperations?: Array<{
        operation: 'ADD' | 'DELETE';
        key?: string;
        mimeType?: string;
      }>;
      description: string;
      customFields?: Array<{
        customFieldId: number;
        value: {
          value: unknown;
        };
      }>;
    }>;
  };

}

/**
 * Interface cho thông tin nâng cao của sản phẩm số khi tạo mới (theo request body mẫu)
 */
export interface DigitalProductAdvancedInfoForCreate {
  /**
   * Số lượt mua
   */
  purchaseCount: number;

  /**
   * Quy trình xử lý đơn hàng số
   */
  digitalFulfillmentFlow: {
    deliveryMethod: 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation';
    deliveryTiming: 'immediate' | 'delayed';
    deliveryDelayMinutes: number;
    accessStatus: 'pending' | 'delivered' | 'not_delivered' | 'delivery_error';
  };

  /**
   * Đầu ra sản phẩm số
   */
  digitalOutput: {
    outputType: 'online_course' | 'file_download' | 'license_key' | 'ebook';
    accessLink?: string;
    loginInfo?: {
      username: string;
      password: string;
    };
    usageInstructions?: string;
  };

  /**
   * Metadata cho các phiên bản sản phẩm (theo cấu trúc request body mẫu)
   */
  variantMetadata?: {
    variants: Array<{
      name: string;
      sku: string;
      availableQuantity: number;
      minQuantityPerPurchase: number;
      maxQuantityPerPurchase: number;
      price: {
        listPrice: number;
        salePrice: number;
        currency: string;
      };
      imagesMediaTypes?: string[];
      customFields?: Array<{
        customFieldId: number;
        value: {
          value: unknown;
        };
      }>;
      description?: string;
    }>;
  };
}


/**
 * Interface cho response khi tạo sản phẩm với upload URLs
 */
export interface CreateProductResponse {
  /**
   * ID của sản phẩm đã tạo
   */
  id: string;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice: string;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách hình ảnh với upload URLs
   */
  images: Array<{
    /**
     * Key của hình ảnh trên S3
     */
    key: string;
    /**
     * Vị trí của hình ảnh
     */
    position: number;
    /**
     * URL để upload ảnh lên cloud
     */
    url: string;
  }>;
}

/**
 * Interface cho response khi cập nhật sản phẩm với upload URLs
 */
export interface UpdateProductResponse extends ProductDto {
  /**
   * URLs để upload ảnh mới (nếu có)
   */
  uploadUrls?: {
    /**
     * ID sản phẩm
     */
    productId: string;
    /**
     * Danh sách URLs để upload ảnh
     */
    imagesUploadUrls: Array<{
      /**
       * URL để upload
       */
      url: string;
      /**
       * Key của ảnh trên S3
       */
      key: string;
      /**
       * Index của ảnh
       */
      index: number;
    }>;
  };
}

/**
 * Interface cho response khi tạo sản phẩm số với upload URLs
 */
export interface CreateDigitalProductResponse {
  /**
   * ID của sản phẩm đã tạo
   */
  id: string;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Loại sản phẩm
   */
  productType: ProductTypeEnum;

  /**
   * Loại giá
   */
  typePrice: PriceTypeEnum;

  /**
   * Giá sản phẩm
   */
  price: {
    listPrice: number;
    salePrice: number;
    currency: string;
  };

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Danh sách hình ảnh với upload URLs
   */
  images?: Array<{
    key: string;
    position: number;
    url: string;
  }>;

  /**
   * Thông tin metadata chứa variants và custom fields
   */
  metadata?: {
    variants: Array<{
      sku: string;
      name: string;
      price: {
        currency: string;
        listPrice: number;
        salePrice: number;
      };
      description: string;
      availableQuantity: number;
      maxQuantityPerPurchase: number;
      minQuantityPerPurchase: number;
      images: Array<{
        key: string;
        position: number;
        url: string;
      }>;
      customFields?: Array<{
        id: number;
        tags: string[];
        type: string;
        label: string;
        value: {
          value: string;
        };
        configId: string;
        required: boolean;
        configJson: {
          validation: Record<string, unknown>;
          displayName: string;
        };
      }>;
    }>;
    customFields: Array<unknown>;
  };

  /**
   * URLs để upload ảnh sản phẩm chính và variants
   */
  uploadUrls?: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
    advancedImagesUploadUrls?: Array<{
      url: string;
      key: string;
      type: string;
      index: number;
      position: number;
    }>;
  };

  /**
   * Danh sách phân loại/variants với upload URLs
   */
  classifications?: Array<{
    id: string;
    type: string;
    price: {
      listPrice: number;
      salePrice: number;
      currency: string;
    };
    images?: Array<{
      key: string;
      position: number;
      url: string;
    }>;
    uploadUrls?: {
      classificationId: string;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    };
    customFields?: Array<{
      customFieldId: number;
      value: {
        value: unknown;
      };
    }>;
  }>;

  /**
   * Thông tin nâng cao
   */
  advancedInfo?: DigitalProductAdvancedInfoForCreate;
}

/**
 * Interface cho phiên bản sản phẩm số (tương tự EventTicketType nhưng không có thời gian bán)
 */
export interface DigitalProductVersion {
  /**
   * Tên phiên bản
   */
  name: string;

  /**
   * Giá phiên bản
   */
  price: number;

  /**
   * Đơn vị tiền tệ
   */
  currency: string;

  /**
   * Mô tả phiên bản
   */
  description: string;

  /**
   * Số lượng có sẵn
   */
  quantity: number;

  /**
   * Số lượng tối thiểu mỗi lần mua
   */
  minQuantityPerPurchase: number;

  /**
   * Số lượng tối đa mỗi lần mua
   */
  maxQuantityPerPurchase: number;

  /**
   * Trạng thái
   */
  status: 'PENDING' | 'ACTIVE' | 'INACTIVE';

  /**
   * Mã SKU
   */
  sku?: string;

  /**
   * Danh sách media types của ảnh phiên bản
   */
  imagesMediaTypes?: string[];
}

/**
 * Interface cho cấu hình sản phẩm số (theo API structure)
 */
export interface DigitalProductConfig {
  /**
   * Số lượng đã mua
   */
  purchaseCount: number;

  /**
   * Quy trình xử lý đơn hàng số
   */
  digitalFulfillmentFlow: {
    /**
     * Phương thức giao hàng
     */
    deliveryMethod: 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation';

    /**
     * Thời điểm giao hàng
     */
    deliveryTiming: 'immediate' | 'delayed';

    /**
     * Thời gian chờ (phút) - chỉ áp dụng khi deliveryTiming = 'delayed'
     */
    deliveryDelayMinutes: number;

    /**
     * Tình trạng truy cập
     */
    accessStatus: 'pending' | 'delivered' | 'not_delivered' | 'delivery_error';
  };

  /**
   * Đầu ra sản phẩm số
   */
  digitalOutput: {
    /**
     * Loại đầu ra
     */
    outputType: 'online_course' | 'file_download' | 'license_key' | 'ebook';

    /**
     * Link truy cập
     */
    accessLink?: string;

    /**
     * Thông tin đăng nhập (cho khóa học online)
     */
    loginInfo?: {
      username: string;
      password: string;
    };

    /**
     * Hướng dẫn sử dụng
     */
    usageInstructions?: string;
  };

  /**
   * Danh sách phiên bản sản phẩm số
   */
  versions?: DigitalProductVersion[];
}

/**
 * Interface cho cấu hình dịch vụ (theo API structure)
 */
export interface ServiceProductConfig {
  /**
   * Số lượng đã mua
   */
  purchaseCount: number;

  /**
   * Danh sách gói dịch vụ
   */
  servicePackages: Array<{
    /**
     * Tên gói dịch vụ
     */
    name: string;

    /**
     * Giá gói dịch vụ
     */
    price: number;

    /**
     * Thời gian bắt đầu (timestamp)
     */
    startTime: number;

    /**
     * Thời gian kết thúc (timestamp)
     */
    endTime: number;

    /**
     * Múi giờ
     */
    timezone: string;

    /**
     * Mô tả gói dịch vụ
     */
    description: string;

    /**
     * Số lượng có sẵn
     */
    quantity: number;

    /**
     * Số lượng tối thiểu mỗi lần mua
     */
    minQuantityPerPurchase: number;

    /**
     * Số lượng tối đa mỗi lần mua
     */
    maxQuantityPerPurchase: number;

    /**
     * Trạng thái
     */
    status: 'PENDING' | 'ACTIVE' | 'INACTIVE';

    /**
     * Danh sách media types của ảnh gói dịch vụ
     */
    imagesMediaTypes?: string[];
  }>;
}

/**
 * Interface cho loại vé sự kiện (theo API structure)
 */
export interface EventTicketType {
  /**
   * Tên loại vé
   */
  name: string;

  /**
   * Giá vé
   */
  price: number;

  /**
   * Thời gian bắt đầu (timestamp)
   */
  startTime: number;

  /**
   * Thời gian kết thúc (timestamp)
   */
  endTime: number;

  /**
   * Múi giờ
   */
  timezone: string;

  /**
   * Mô tả loại vé
   */
  description: string;

  /**
   * Số lượng có sẵn
   */
  quantity: number;

  /**
   * Số lượng tối thiểu mỗi lần mua
   */
  minQuantityPerPurchase: number;

  /**
   * Số lượng tối đa mỗi lần mua
   */
  maxQuantityPerPurchase: number;

  /**
   * Trạng thái
   */
  status: 'PENDING' | 'ACTIVE' | 'INACTIVE';

  /**
   * Danh sách media types của ảnh vé
   */
  imagesMediaTypes?: string[];
}

/**
 * Interface cho cấu hình sự kiện (theo API structure)
 */
export interface EventProductConfig {
  /**
   * Số lượng đã mua
   */
  purchaseCount: number;

  /**
   * Hình thức sự kiện
   */
  eventFormat: 'ONLINE' | 'OFFLINE' | 'HYBRID';

  /**
   * Link sự kiện (cho online/hybrid)
   */
  eventLink?: string;

  /**
   * Địa điểm sự kiện (cho offline/hybrid)
   */
  eventLocation?: string;

  /**
   * Thời gian bắt đầu sự kiện (timestamp)
   */
  startDate: number;

  /**
   * Thời gian kết thúc sự kiện (timestamp)
   */
  endDate: number;

  /**
   * Múi giờ
   */
  timezone: string;

  /**
   * Danh sách loại vé
   */
  ticketTypes: EventTicketType[];
}

/**
 * Interface cho cấu hình combo sản phẩm
 */
export interface ComboProductConfig {
  /**
   * Danh sách sản phẩm trong combo
   */
  comboProducts: Array<{
    /**
     * ID sản phẩm
     */
    productId: number;

    /**
     * Số lượng
     */
    quantity: number;

    /**
     * Phần trăm giảm giá
     */
    discountPercent?: number;
  }>;
}

/**
 * Interface cho cập nhật sản phẩm sự kiện với đầy đủ thông tin
 */
export interface UpdateEventProductDto {
  /**
   * Tên sản phẩm
   */
  name?: string;

  /**
   * Giá sản phẩm
   */
  price?: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice?: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách thao tác ảnh
   */
  images?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Danh sách custom fields cho sản phẩm
   */
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: unknown;
    };
  }>;

  /**
   * Thông tin nâng cao cho sản phẩm sự kiện
   */
  advancedInfo?: EventProductConfig;

  /**
   * Danh sách media types của ảnh sản phẩm (bao gồm cả ảnh ticket)
   */
  imagesMediaTypes?: string[];
}
