import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  IconCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import { z } from 'zod';
import {
  PriceTypeEnum,
  ProductTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
} from '../../types/product.types';
import { useCreateProduct } from '../../hooks/useProductQuery';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';
import {
  GeneralInfoSection,
  PricingSection,
  MediaSection,
  InventorySection,
  ShippingSection,
  ClassificationsSection,
  CustomFieldsSection,
} from './sections';



interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
  classifications?: Array<{
    id: number;
    type: string;
    price: {
      listPrice: number;
      salePrice: number;
      currency: string;
      value: number;
    };
    customFields: Array<{
      customFieldId: number;
      value: {
        value: string;
      };
    }>;
    imagesMediaTypes: string[];
    images: Array<{
      key: string;
      position: number;
      url: string;
    }>;
    uploadUrls: {
      classificationId: number;
      imagesUploadUrls: Array<{
        url: string;
        key: string;
        index: number;
      }>;
    };
  }>;
}

interface ProductFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
interface ProductFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: ProductInventory[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Interface cho inventory trong form
export interface ProductInventory {
  id: number; // ID tạm thời cho UI
  warehouseId?: string | number;
  availableQuantity?: string | number;
  sku?: string;
  barcode?: string;
}

// Extended FileWithMetadata interface for variant images
export interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string | undefined;
  name?: string | undefined;
  size?: number | undefined;
  type?: string | undefined;
}

// Interface cho biến thể sản phẩm trong form
export interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  priceDescription?: string; // Mô tả giá cho biến thể (khi typePrice = STRING_PRICE)
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  // ✅ Quản lý tồn kho và ảnh cho mỗi biến thể
  sku?: string; // SKU riêng cho biến thể
  availableQuantity?: string | number; // Số lượng có sẵn
  images?: ExtendedFileWithMetadata[]; // Nhiều ảnh cho biến thể
  customFields: SelectedCustomField[];
}

/**
 * Form tạo sản phẩm mới
 */
const ProductForm: React.FC<ProductFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['business', 'common']);

  // Hook để tạo sản phẩm mới
  const createProductMutation = useCreateProduct();

  // Schema validation với giá cố định
  const productSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      listPrice: z.union([z.string(), z.number()]),
      salePrice: z.union([z.string(), z.number()]),
      currency: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      shipmentConfig: z
        .object({
          lengthCm: z.union([z.string(), z.number()]).optional(),
          widthCm: z.union([z.string(), z.number()]).optional(),
          heightCm: z.union([z.string(), z.number()]).optional(),
          weightGram: z.union([z.string(), z.number()]).optional(),
        })
        .optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      classifications: z.any().optional(), // Đổi tên từ variants thành classifications
      inventory: z
        .array(
          z.object({
            id: z.number(),
            warehouseId: z.union([z.string(), z.number()]).optional(),
            availableQuantity: z.union([z.string(), z.number()]).optional(),
            sku: z.string().optional(),
            barcode: z.string().optional(),
          })
        )
        .optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra listPrice
      if (!data.listPrice || data.listPrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá niêm yết',
          path: ['listPrice'],
        });
      } else {
        const listPrice = Number(data.listPrice);
        if (isNaN(listPrice) || listPrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá niêm yết phải là số >= 0',
            path: ['listPrice'],
          });
        }
      }

      // Kiểm tra salePrice
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      } else {
        const salePrice = Number(data.salePrice);
        if (isNaN(salePrice) || salePrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá bán phải là số >= 0',
            path: ['salePrice'],
          });
        }
      }

      // Kiểm tra currency
      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
        const listPrice = Number(data.listPrice);
        const salePrice = Number(data.salePrice);

        if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
          if (listPrice <= salePrice) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải lớn hơn giá bán',
              path: ['listPrice'],
            });
          }
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho phân loại sản phẩm (đổi tên từ variants)
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho danh sách inventory
  const [productInventories, setProductInventories] = useState<ProductInventory[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Effect để đồng bộ form values với state khi submit
  useEffect(() => {
    // Cập nhật form values với productInventories khi state thay đổi
    if (formRef.current && productInventories.length > 0) {
      const formValues = formRef.current.getValues();
      formRef.current.setValues({
        ...formValues,
        inventory: productInventories,
      });
    }
  }, [productInventories]);

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    // Test đơn giản trước
    if (!values['name']) {
      console.error('❌ Missing required fields:', {
        name: values['name'],
      });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ProductFormValues;
      setIsUploading(true);
      // Chuyển đổi giá trị form thành dữ liệu API (đã sync với Backend)
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.PHYSICAL,
        typePrice: PriceTypeEnum.HAS_PRICE, // Sử dụng giá cố định
        price: priceData,
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (formValues.tags && formValues.tags.length > 0) {
        productData.tags = formValues.tags;
      }

      const shipmentConfig = getShipmentConfig(formValues);
      if (shipmentConfig && Object.keys(shipmentConfig).length > 0) {
        productData.shipmentConfig = shipmentConfig;
      }

      if (mediaFiles.length > 0) {
        productData.imagesMediaTypes = mediaFiles.map(file => file.file.type);
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Lọc ra những field có giá trị không rỗng
          const fieldValue = field.value?.['value'];
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      if (productClassifications.length > 0) {
        productData.classifications = productClassifications.map(variant => {
          const classification: {
            type: string;
            price: {
              listPrice: number;
              salePrice: number;
              currency: string;
            };
            customFields?: Array<{
              customFieldId: number;
              value: {
                value: unknown;
              };
            }>;
            imagesMediaTypes?: string[];
          } = {
            type: variant.name,
            price: {
              listPrice: Number(variant.listPrice) || 0,
              salePrice: Number(variant.salePrice) || 0,
              currency: variant.currency,
            },
          };

          // Thêm custom fields nếu có
          const filteredCustomFields = variant.customFields
            .filter(field => {
              // Lọc ra những field có giá trị không rỗng
              const fieldValue = field.value?.['value'];
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: {
                value: field.value?.['value'],
              },
            }));

          if (filteredCustomFields.length > 0) {
            classification.customFields = filteredCustomFields;
          }

          // Thêm media types nếu có ảnh
          if (variant.images && variant.images.length > 0) {
            const imageTypes = variant.images
              .map(img => img.type)
              .filter((type): type is string => Boolean(type && type.trim()));
            if (imageTypes.length > 0) {
              classification.imagesMediaTypes = imageTypes;
            }
          }

          return classification;
        });
      }

      // Sử dụng productInventories state thay vì form values (hỗ trợ nhiều kho)
      const inventoryData = getInventoryDataFromState();
      if (inventoryData) {
        productData.inventory = inventoryData;
      }


      // Gọi API tạo sản phẩm
      const response = await createProductMutation.mutateAsync(productData);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray((response.uploadUrls as Record<string, unknown>)['imagesUploadUrls']);

          if (hasUploadUrls) {
            const uploadUrls = (response.uploadUrls as Record<string, unknown>)['imagesUploadUrls'] as Array<{
              url: string;
              key: string;
              index: number;
            }>;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });
              // Tạo array các file và URLs để upload cùng lúc
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            } else {
              NotificationUtil.warning({
                message: t(
                  'business:product.mediaUploadWarning',
                  'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
                ),
                duration: 5000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // Upload ảnh cho classifications (biến thể) nếu có
      const responseWithClassifications = response as unknown as ProductWithUploadUrlsResponse;
      if (responseWithClassifications && responseWithClassifications.classifications && Array.isArray(responseWithClassifications.classifications)) {
        try {
          // Lọc các classifications có ảnh cần upload
          const classificationsWithImages = productClassifications.filter(variant =>
            variant.images && variant.images.length > 0
          );

          if (classificationsWithImages.length > 0) {
            // Upload ảnh cho từng classification
            for (let classificationIndex = 0; classificationIndex < classificationsWithImages.length; classificationIndex++) {
              const variant = classificationsWithImages[classificationIndex];

              if (!variant) {
                console.warn(`⚠️ Variant not found at index ${classificationIndex}`);
                continue;
              }
              // Tìm classification tương ứng theo index hoặc theo tên
              // Vì backend tạo classifications theo thứ tự của productClassifications
              const classification = responseWithClassifications.classifications[classificationIndex] ||
                responseWithClassifications.classifications.find(c =>
                  c.type === variant.name && c.uploadUrls && c.uploadUrls.classificationId
                );

              if (classification && classification.uploadUrls && classification.uploadUrls['imagesUploadUrls'] && variant.images) {
                const uploadUrls = classification.uploadUrls['imagesUploadUrls'];

                if (uploadUrls.length > 0) {
                  // Upload từng ảnh của classification
                  for (let imageIndex = 0; imageIndex < Math.min(variant.images.length, uploadUrls.length); imageIndex++) {
                    const imageFile = variant.images[imageIndex];
                    const uploadUrlInfo = uploadUrls[imageIndex];

                    if (!imageFile || !uploadUrlInfo) {
                      continue;
                    }

                    const uploadUrl = uploadUrlInfo.url;

                    if (imageFile.file && uploadUrl) {
                      try {
                        await uploadProductImages([{ file: imageFile.file, id: `classification-${variant.id}-${imageIndex}` }], [uploadUrl], {
                          skipCacheInvalidation: true,
                        });
                      } catch (classificationImageUploadError) {
                        console.error(`❌ Error uploading classification image ${imageIndex + 1} for ${variant.name}:`, classificationImageUploadError);
                      }
                    }
                  }
                }
              } else {
                console.warn(`⚠️ No classification found for variant: ${variant.name}`);
              }
            }

            NotificationUtil.success({
              message: 'Tải lên ảnh phân loại thành công',
              duration: 3000,
            });
          }
        } catch (classificationUploadError) {
          console.error('❌ Error uploading classification images:', classificationUploadError);
          NotificationUtil.warning({
            message: 'Có lỗi xảy ra khi tải lên ảnh phân loại',
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess', 'Tạo sản phẩm thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setIsUploading(false);

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        NotificationUtil.error({
          message: t('business:product.validationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.createError', 'Có lỗi xảy ra khi tạo sản phẩm'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá cố định
  const getPriceData = (values: ProductFormValues) => {
    // Kiểm tra đầy đủ các trường bắt buộc
    if (!values.listPrice || values.listPrice === '') {
      throw new Error('Vui lòng nhập giá niêm yết');
    }

    if (!values.salePrice || values.salePrice === '') {
      throw new Error('Vui lòng nhập giá bán');
    }

    if (!values.currency || values.currency.trim() === '') {
      throw new Error('Vui lòng chọn đơn vị tiền tệ');
    }

    const listPrice = Number(values.listPrice);
    const salePrice = Number(values.salePrice);

    if (isNaN(listPrice) || listPrice < 0) {
      throw new Error('Giá niêm yết phải là số >= 0');
    }

    if (isNaN(salePrice) || salePrice < 0) {
      throw new Error('Giá bán phải là số >= 0');
    }

    // Kiểm tra giá niêm yết phải lớn hơn giá bán
    if (listPrice <= salePrice) {
      throw new Error('Giá niêm yết phải lớn hơn giá bán');
    }

    return {
      listPrice,
      salePrice,
      currency: values.currency.trim(),
    };
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển
  const getShipmentConfig = (values: ProductFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    const result: {
      lengthCm?: number;
      widthCm?: number;
      heightCm?: number;
      weightGram?: number;
    } = {};

    if (config.lengthCm) {
      const lengthCm = Number(config.lengthCm);
      if (!isNaN(lengthCm) && lengthCm > 0) {
        result.lengthCm = lengthCm;
      }
    }

    if (config.widthCm) {
      const widthCm = Number(config.widthCm);
      if (!isNaN(widthCm) && widthCm > 0) {
        result.widthCm = widthCm;
      }
    }

    if (config.heightCm) {
      const heightCm = Number(config.heightCm);
      if (!isNaN(heightCm) && heightCm > 0) {
        result.heightCm = heightCm;
      }
    }

    if (config.weightGram) {
      const weightGram = Number(config.weightGram);
      if (!isNaN(weightGram) && weightGram > 0) {
        result.weightGram = weightGram;
      }
    }

    return result;
  };



  // Hàm lấy dữ liệu tồn kho từ state (trả về array để hỗ trợ nhiều kho)
  const getInventoryDataFromState = () => {
    if (productInventories.length === 0) {
      return undefined;
    }

    // Lọc và chuyển đổi tất cả inventory có warehouseId hợp lệ
    const validInventories = productInventories
      .filter(inventory => inventory.warehouseId)
      .map(inventory => {
        const inventoryData: {
          warehouseId: number;
          availableQuantity?: number;
          sku?: string;
          barcode?: string;
        } = {
          warehouseId: Number(inventory.warehouseId),
        };

        if (inventory.availableQuantity) {
          const quantity = Number(inventory.availableQuantity);
          if (!isNaN(quantity) && quantity >= 0) {
            inventoryData.availableQuantity = quantity;
          }
        }

        if (inventory.sku && inventory.sku.trim()) {
          inventoryData.sku = inventory.sku.trim();
        }

        if (inventory.barcode && inventory.barcode.trim()) {
          inventoryData.barcode = inventory.barcode.trim();
        }

        return inventoryData;
      });

    return validInventories.length > 0 ? validInventories : undefined;
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        // Kiểm tra xem trường đã tồn tại chưa
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          // Nếu đã tồn tại, xóa nó (bỏ chọn)
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Thêm trường mới với thông tin đầy đủ
        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
          type: (fieldData?.['type'] as string) || 'text',
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Handlers cho inventory
  const handleAddInventory = useCallback(() => {
    const newInventory: ProductInventory = {
      id: Date.now(),
      warehouseId: '',
      availableQuantity: '',
      sku: '',
      barcode: '',
    };
    setProductInventories(prev => [...prev, newInventory]);
  }, []);

  const handleRemoveInventory = useCallback((inventoryId: number) => {
    setProductInventories(prev => prev.filter(inventory => inventory.id !== inventoryId));
  }, []);

  const handleUpdateInventory = useCallback((inventoryId: number, field: string | number | symbol, value: string | number) => {
    setProductInventories(prev =>
      prev.map(inventory =>
        inventory.id === inventoryId ? { ...inventory, [field]: value } : inventory
      )
    );
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phân loại mới (đổi tên từ variant)
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(), // ID tạm thời cho UI
      name: '',
      priceDescription: '', // Mô tả giá cho biến thể
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      // ✅ Giá trị mặc định cho tồn kho và ảnh
      sku: '',
      availableQuantity: '',
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [],
    };

    setProductClassifications(prev => [...prev, newVariant]);
    // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
  }, []);

  // Xóa phân loại (đổi tên từ variant)
  const handleRemoveVariant = useCallback((variantId: number) => {
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
    // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
  }, []);

  // Cập nhật phân loại (đổi tên từ variant)
  const handleUpdateVariant = useCallback(
    (variantId: number, field: string | number | symbol, value: string | number) => {
      // ✅ Validation cho số lượng
      let validatedValue = value;

      // Validate số lượng không được âm
      if (field === 'availableQuantity') {
        const numValue = Number(value);
        if (numValue < 0) {
          validatedValue = 0;
        }
      }

      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, [field]: validatedValue };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // ✅ THÊM MỚI: Xử lý upload nhiều ảnh cho biến thể
  const handleVariantImagesChange = useCallback(
    (variantId: number, files: ExtendedFileWithMetadata[]) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, images: files };
          }
          return variant;
        })
      );
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phân loại (đổi tên từ variant)
  const handleToggleCustomFieldToVariant = useCallback(
    (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            // Kiểm tra xem trường đã tồn tại trong phân loại chưa
            const existingFieldIndex = variant.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Nếu đã tồn tại, xóa nó (bỏ chọn)
              return {
                ...variant,
                customFields: variant.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Thêm trường mới vào phân loại với thông tin đầy đủ
            return {
              ...variant,
              customFields: [
                ...variant.customFields,
                {
                  id: Date.now(), // ID tạm thời
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component:
                    (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
                  type: (fieldData?.['type'] as string) || 'text',
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: '' }, // Giá trị mặc định
                },
              ],
            };
          }
          return variant;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phân loại (đổi tên từ variant)
  const handleRemoveCustomFieldFromVariant = useCallback(
    (variantId: number, customFieldId: number) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phân loại (đổi tên từ variant)
  const handleUpdateCustomFieldInVariant = useCallback(
    (variantId: number, customFieldId: number, value: string) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return variant;
        })
      );
      // Không cần setValue cho classifications vì chúng ta quản lý riêng trong state
    },
    []
  );

  // Giá trị mặc định cho form - sử dụng useMemo để tránh re-create
  const defaultValues = useMemo(
    () => ({
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      description: '',
      tags: [],
      shipmentConfig: {
        lengthCm: '',
        widthCm: '',
        heightCm: '',
        weightGram: '',
      },
      inventory: [],
      customFields: [],
      media: [],
      classifications: [], // Đổi tên từ variants
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createTitle')}>
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });
          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        <GeneralInfoSection
          tempTags={tempTags}
          setTempTags={setTempTags}
        />

        <PricingSection />

        <MediaSection
          mediaFiles={mediaFiles}
          setMediaFiles={setMediaFiles}
          tempTags={tempTags}
          setTempTags={setTempTags}
        />
        <InventorySection
          productInventories={productInventories}
          setProductInventories={setProductInventories}
          handleAddInventory={handleAddInventory}
          handleRemoveInventory={handleRemoveInventory}
          handleUpdateInventory={handleUpdateInventory}
        />
        <ShippingSection />

        <ClassificationsSection
          productClassifications={productClassifications}
          setProductClassifications={setProductClassifications}
          handleAddVariant={handleAddVariant}
          handleRemoveVariant={handleRemoveVariant}
          handleUpdateVariant={handleUpdateVariant}
          handleVariantImagesChange={handleVariantImagesChange}
          handleToggleCustomFieldToVariant={handleToggleCustomFieldToVariant}
          handleUpdateCustomFieldInVariant={handleUpdateCustomFieldInVariant}
          handleRemoveCustomFieldFromVariant={handleRemoveCustomFieldFromVariant}
        />

        <CustomFieldsSection
          productCustomFields={productCustomFields}
          setProductCustomFields={setProductCustomFields}
          handleToggleCustomFieldToProduct={handleToggleCustomFieldToProduct}
          handleUpdateCustomFieldInProduct={handleUpdateCustomFieldInProduct}
          handleRemoveCustomFieldFromProduct={handleRemoveCustomFieldFromProduct}
        />

        <div className="flex flex-row justify-end gap-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={createProductMutation.isPending || isUploading}
          />
          <IconCard
            icon="check"
            variant="primary"
            size="md"
            title={
              createProductMutation.isPending || isUploading
                ? t('business:product.creating', 'Đang tạo...')
                : t('common:save', 'Lưu')
            }
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={createProductMutation.isPending || isUploading}
            isLoading={createProductMutation.isPending || isUploading}
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ProductForm;
