import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
  Textarea,
} from '@/shared/components/common';
import { DigitalOutputSectionProps } from './digital-product-form-types';

const DigitalOutputSection: React.FC<DigitalOutputSectionProps> = () => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.digitalOutput', '4. <PERSON><PERSON><PERSON> ra sản phẩm số')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="digitalProductType" label={t('business:product.form.digitalProduct.digitalProductType.title')} required>
          <Select
            fullWidth
            options={[
              { value: 'ACCESS_CODE', label: t('business:product.form.digitalProduct.digitalProductType.onlineCourse') },
              { value: 'DOWNLOAD_LINK', label: t('business:product.form.digitalProduct.digitalProductType.fileDownload') },
              { value: 'ACCOUNT_INFO', label: t('business:product.form.digitalProduct.digitalProductType.licenseKey') },
              { value: 'CONTENT', label: t('business:product.form.digitalProduct.digitalProductType.ebook') },
            ]}
          />
        </FormItem>

        <FormItem name="accessLink" label={t('business:product.form.digitalProduct.accessLink')}>
          <Input fullWidth placeholder={t('business:product.form.digitalProduct.accessLinkPlaceholder')} />
        </FormItem>

        <FormItem name="usageInstructions" label={t('business:product.form.digitalProduct.usageInstructions')}>
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.digitalProduct.usageInstructionsPlaceholder')}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default DigitalOutputSection;
