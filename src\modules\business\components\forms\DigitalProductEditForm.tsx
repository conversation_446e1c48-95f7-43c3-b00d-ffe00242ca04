import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { Controller } from 'react-hook-form';
import { NotificationUtil } from '@/shared/utils/notification';
import { useProduct, useUpdateDigitalProduct, PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';
import { useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  DigitalProductConfig,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { DigitalProductVersion } from '../../types/product.types';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho file với metadata mở rộng
interface ExtendedFileWithMetadata extends FileWithMetadata {
  name?: string;
  size?: number;
  type?: string;
}

// Interface cho phiên bản sản phẩm số trong form (tương tự FormEventTicketType)
interface FormDigitalProductVersion extends Partial<DigitalProductVersion> {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  description?: string;
  quantity?: number;
  sku?: string;
  minQuantityPerPurchase?: number;
  maxQuantityPerPurchase?: number;
  // Bỏ status và thêm images và customFields
  images?: ExtendedFileWithMetadata[]; // Nhiều ảnh cho phiên bản
  customFields: SelectedCustomField[];
}

// Interface cho form values
interface DigitalProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string;
  salePrice?: string;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  deliveryMethod: DigitalProductConfig['digitalFulfillmentFlow']['deliveryMethod'];
  deliveryTiming: DigitalProductConfig['digitalFulfillmentFlow']['deliveryTiming'];
  digitalProductType: DigitalProductConfig['digitalOutput']['outputType'];
  accessLink?: string;
  usageInstructions?: string;
  versions: FormDigitalProductVersion[];
}

interface DigitalProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
}

const DigitalProductEditForm: React.FC<DigitalProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  // Gọi API lấy chi tiết sản phẩm
  const { data: product, isLoading: isLoadingProduct } = useProduct(productId);
  // Hook để cập nhật sản phẩm số
  const updateDigitalProductMutation = useUpdateDigitalProduct();
  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);
  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho phiên bản sản phẩm số
  const [versions, setVersions] = useState<FormDigitalProductVersion[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm
  const { uploadProductImages } = useProductImageUpload();

  // Schema validation cho sản phẩm số (bao gồm cả digital product fields)
  const digitalProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      // Các trường digital product được validate và gửi lên API (theo backend enum)
      deliveryMethod: z.enum(['EMAIL', 'DASHBOARD', 'SMS', 'DIRECT_MESSAGE', 'ZALO', 'AUTO_ACTIVE']),
      deliveryTiming: z.enum(['IMMEDIATE', 'DELAYED']),
      digitalProductType: z.enum(['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT']),
      accessLink: z.string().optional(),
      usageInstructions: z.string().optional(),
      // Validation cho phiên bản
      versions: z.array(z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Tên phiên bản không được để trống'),
        price: z.number().min(0, 'Giá phiên bản phải >= 0'),
        currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
        description: z.string().optional(),
        quantity: z.number().min(1, 'Số lượng phải >= 1').optional(),
        sku: z.string().optional(),
        minQuantityPerPurchase: z.number().min(1, 'Số lượng tối thiểu phải >= 1').optional(),
        maxQuantityPerPurchase: z.number().min(1, 'Số lượng tối đa phải >= 1').optional(),
      })).optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // Khởi tạo giá trị mặc định từ product data
  const defaultValues = useMemo(() => {
    if (!product) return {};
    const price = product.price as HasPriceDto | StringPriceDto;
    const hasPrice = product.typePrice === PriceTypeEnum.HAS_PRICE;
    const stringPrice = product.typePrice === PriceTypeEnum.STRING_PRICE;

    // Extract digital-specific data from advancedInfo
    const advancedInfo = (product as unknown as Record<string, unknown>)['advancedInfo'] as Record<string, unknown> | undefined;
    const digitalFulfillmentFlow = advancedInfo?.['digitalFulfillmentFlow'] as Record<string, unknown> | undefined;
    const digitalOutput = advancedInfo?.['digitalOutput'] as Record<string, unknown> | undefined;

    return {
      name: product.name || '',
      typePrice: product.typePrice || PriceTypeEnum.HAS_PRICE,
      listPrice: hasPrice ? (price as HasPriceDto).listPrice?.toString() || '' : '',
      salePrice: hasPrice ? (price as HasPriceDto).salePrice?.toString() || '' : '',
      currency: hasPrice ? (price as HasPriceDto).currency || 'VND' : 'VND',
      priceDescription: stringPrice ? (price as StringPriceDto).priceDescription || '' : '',
      description: product.description || '',
      tags: product.tags || [],
      // Digital product defaults từ advancedInfo response
      deliveryMethod: (digitalFulfillmentFlow?.['deliveryMethod'] as string) || 'dashboard_download',
      deliveryTiming: (digitalFulfillmentFlow?.['deliveryTiming'] as string) || 'immediate',
      digitalProductType: (digitalOutput?.['outputType'] as string) || 'file_download',
      accessLink: (digitalOutput?.['accessLink'] as string) || '',
      usageInstructions: (digitalOutput?.['usageInstructions'] as string) || '',
      // Phiên bản mặc định
      versions: [],
    };
  }, [product]);

  // Sync tags với state
  useEffect(() => {
    if (product?.tags) {
      setTempTags(product.tags);
    }
  }, [product?.tags]);

  // Sync custom fields với state từ metadata.customFields
  useEffect(() => {
    if (product?.metadata?.customFields && product.metadata.customFields.length > 0) {
      const existingCustomFields: SelectedCustomField[] = product.metadata.customFields.map((field) => {
        return {
          id: field.id,
          fieldId: field.id, // Sử dụng id làm fieldId
          label: field.label,
          component: field.type, // component = type từ API
          type: field.type,
          required: field.required || false,
          configJson: field.configJson || {},
          value: field.value || { value: '' },
        };
      });

      setProductCustomFields(existingCustomFields);
    } else {
      setProductCustomFields([]);
    }
  }, [product?.metadata?.customFields]);

  // Sync ảnh hiện có với state
  useEffect(() => {
    if (product?.images && product.images.length > 0) {
      // Chuyển đổi ảnh hiện có thành FileWithMetadata format
      const existingImages: FileWithMetadata[] = product.images
        .sort((a, b) => a.position - b.position) // Sắp xếp theo position
        .map((image, index) => {
          // Tạo tên file từ key hoặc sử dụng tên mặc định
          const fileName = image.key?.split('/').pop() || `image-${index}.jpg`;
          // Tạo một File object placeholder với thông tin cơ bản
          const placeholderFile = new File([], fileName, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });

          return {
            id: `existing-${image.key}-${index}`, // Prefix "existing-" để phân biệt với ảnh mới
            file: placeholderFile,
            preview: image.url, // Sử dụng URL hiện có làm preview
          };
        });

      setMediaFiles(existingImages);
    } else {
      // Nếu không có ảnh, reset state
      setMediaFiles([]);
    }
  }, [product?.images]);

  // Sync versions từ metadata.variants (theo response API mới)
  useEffect(() => {
    console.log('🔄 [DigitalProductEditForm] Syncing versions from product data:', product);
    const productData = product as unknown as Record<string, unknown>;
    const metadata = productData?.['metadata'] as Record<string, unknown> | undefined;
    const variants = metadata?.['variants'] as Record<string, unknown>[] | undefined;

    if (variants && Array.isArray(variants)) {
      console.log('📦 [DigitalProductEditForm] Found variants in metadata:', variants.length);
      const existingVersions: FormDigitalProductVersion[] = variants.map((variant, index) => {
        // Convert variant images to ExtendedFileWithMetadata format
        const variantImages: ExtendedFileWithMetadata[] = Array.isArray(variant['images'])
          ? (variant['images'] as Record<string, unknown>[]).map((img: Record<string, unknown>, imgIndex: number) => {
              const urlParts = (img['url'] as string).split('/');
              const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `variant-image-${imgIndex + 1}`;

              return {
                id: `variant-${index}-existing-${img['key']}`,
                file: new File([], fileName, { type: 'image/jpeg' }),
                preview: img['url'] as string,
                name: fileName,
                size: 0,
                type: 'image/jpeg',
              };
            })
          : [];

        const variantPrice = variant['price'] as Record<string, unknown> | undefined;

        // Convert customFields từ metadata.variants format
        const customFields: SelectedCustomField[] = Array.isArray(variant['customFields'])
          ? (variant['customFields'] as Record<string, unknown>[]).map((field, fieldIndex) => ({
              id: Date.now() + fieldIndex + index * 1000, // Unique ID cho mỗi field
              fieldId: (field['id'] as number) || 0, // Sử dụng id từ API
              label: (field['label'] as string) || `Custom Field ${field['id']}`,
              component: (field['type'] as string) || 'text',
              type: (field['type'] as string) || 'text',
              required: (field['required'] as boolean) || false,
              configJson: (field['configJson'] as Record<string, unknown>) || {},
              value: (field['value'] as Record<string, unknown>) || { value: '' },
            }))
          : [];

        return {
          id: `version-${Date.now()}-${index}`,
          name: (variant['name'] as string) || `Version ${index + 1}`,
          price: (variantPrice?.['listPrice'] as number) || (variantPrice?.['salePrice'] as number) || 0,
          currency: (variantPrice?.['currency'] as string) || 'VND',
          description: (variant['description'] as string) || `Phiên bản ${variant['name']}`,
          quantity: (variant['availableQuantity'] as number) || 1,
          sku: (variant['sku'] as string) || `${(variant['name'] as string || 'VERSION').toUpperCase()}-${String(index + 1).padStart(3, '0')}`,
          minQuantityPerPurchase: (variant['minQuantityPerPurchase'] as number) || 1,
          maxQuantityPerPurchase: (variant['maxQuantityPerPurchase'] as number) || 10,
          images: variantImages,
          customFields: customFields,
        };
      });

      console.log('✅ [DigitalProductEditForm] Setting versions:', existingVersions.length);
      setVersions(existingVersions);
    } else {
      console.log('📭 [DigitalProductEditForm] No variants found, clearing versions');
      setVersions([]);
    }
  }, [product]);

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = useCallback((values: DigitalProductFormValues): HasPriceDto | StringPriceDto => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }
      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error('Loại giá không hợp lệ');
  }, []);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phiên bản mới
  const handleAddVersion = useCallback(() => {
    const newVersion: FormDigitalProductVersion = {
      id: `version-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      description: '',
      quantity: 1,
      minQuantityPerPurchase: 1,
      maxQuantityPerPurchase: 10,
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [], // Khởi tạo mảng trường tùy chỉnh rỗng
    };
    setVersions(prev => [...prev, newVersion]);
  }, []);

  // Xóa phiên bản
  const handleRemoveVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.filter(version => version.id !== versionId));
  }, []);

  // Cập nhật thông tin phiên bản
  const handleUpdateVersion = useCallback((versionId: string, field: keyof FormDigitalProductVersion, value: string | number) => {
    setVersions(prev => prev.map(version => {
      if (version.id === versionId) {
        return {
          ...version,
          [field]: value
        };
      }
      return version;
    }));
  }, []);

  // Xử lý upload nhiều ảnh cho phiên bản
  const handleVersionImagesChange = useCallback(
    (versionId: string, files: ExtendedFileWithMetadata[]) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return { ...version, images: files };
          }
          return version;
        })
      );
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phiên bản
  const handleToggleCustomFieldToVersion = useCallback(
    (versionId: string, fieldId: number, fieldData?: Record<string, unknown>) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            // Kiểm tra xem trường đã tồn tại trong phiên bản chưa
            const existingFieldIndex = version.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Xóa trường nếu đã tồn tại
              return {
                ...version,
                customFields: version.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Xác định giá trị mặc định dựa trên kiểu dữ liệu
            const fieldType = (fieldData?.['type'] as string) || 'text';
            const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

            let defaultValue: string | number | boolean = '';

            // Xác định giá trị mặc định dựa trên type hoặc component
            if (fieldType === 'number' || fieldComponent === 'number') {
              defaultValue = 0;
            } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
              defaultValue = false;
            } else {
              defaultValue = '';
            }

            // Thêm trường mới vào phiên bản với thông tin đầy đủ
            return {
              ...version,
              customFields: [
                ...version.customFields,
                {
                  id: Date.now(),
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component: fieldComponent,
                  type: fieldType,
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: defaultValue },
                },
              ],
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phiên bản
  const handleRemoveCustomFieldFromVersion = useCallback(
    (versionId: string, customFieldId: number) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phiên bản
  const handleUpdateCustomFieldInVersion = useCallback(
    (versionId: string, customFieldId: number, value: string | number | boolean) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Typography variant="body1">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  // Hiển thị lỗi nếu không tải được sản phẩm
  if (!product) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </FormMultiWrapper>
    );
  }

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    if (!values['name'] || !values['typePrice']) {
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as DigitalProductFormValues;
      setIsUploading(true);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo custom fields trước để sử dụng trong advancedInfo
      const filteredCustomFields = productCustomFields
        .filter(field => {
          const fieldValue = field.value?.['value'];
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      // Tạo advancedInfo theo format request body mới, sử dụng dữ liệu từ API response
      const productAdvancedInfo = (product as unknown as Record<string, unknown>)['advancedInfo'] as Record<string, unknown> | undefined;
      const existingDigitalFulfillmentFlow = productAdvancedInfo?.['digitalFulfillmentFlow'] as Record<string, unknown> | undefined;
      const existingDigitalOutput = productAdvancedInfo?.['digitalOutput'] as Record<string, unknown> | undefined;
      const existingLoginInfo = existingDigitalOutput?.['loginInfo'] as Record<string, unknown> | undefined;

      const advancedInfo = {
        purchaseCount: (productAdvancedInfo?.['purchaseCount'] as number) || 150,
        digitalFulfillmentFlow: {
          deliveryMethod: formValues.deliveryMethod || (existingDigitalFulfillmentFlow?.['deliveryMethod'] as string) || 'dashboard_download',
          deliveryTiming: formValues.deliveryTiming || (existingDigitalFulfillmentFlow?.['deliveryTiming'] as string) || 'immediate',
          deliveryDelayMinutes: (existingDigitalFulfillmentFlow?.['deliveryDelayMinutes'] as number) || 0,
          accessStatus: (existingDigitalFulfillmentFlow?.['accessStatus'] as string) || 'delivered',
        },
        digitalOutput: {
          outputType: formValues.digitalProductType || (existingDigitalOutput?.['outputType'] as string) || 'file_download',
          accessLink: formValues.accessLink || (existingDigitalOutput?.['accessLink'] as string) || 'https://course.example.com/activate?token=updated123',
          loginInfo: {
            username: (existingLoginInfo?.['username'] as string) || 'auto_generated',
            password: (existingLoginInfo?.['password'] as string) || 'new_temp_password'
          },
          usageInstructions: formValues.usageInstructions || (existingDigitalOutput?.['usageInstructions'] as string) || 'Vui lòng đăng nhập bằng thông tin mới để truy cập khóa học',
        },
        // Thêm variantMetadata với variants theo format request body mới
        variantMetadata: {
          variants: versions.length > 0 ? versions.map((version, versionIndex) => {
            // Xử lý images operations cho version - CHỈ xử lý ảnh thay đổi
            const versionImageOperations: Record<string, unknown>[] = [];

            // Tìm original images của version này từ metadata.variants
            const productData = product as unknown as Record<string, unknown>;
            const metadata = productData?.['metadata'] as Record<string, unknown> | undefined;
            const variants = (metadata?.['variants'] as Record<string, unknown>[]) || [];

            // Tìm variant gốc theo name để lấy original images
            const originalVariant = variants.find(v => (v['name'] as string) === version.name);
            const originalVersionImages = (originalVariant?.['images'] as Record<string, unknown>[]) || [];

            // Tìm ảnh existing hiện tại trong version.images
            const currentVersionExistingIds = (version.images || [])
              .filter(file => file.id.startsWith('variant-') && file.id.includes('existing'))
              .map(file => {
                // Extract key từ ID: variant-0-existing-key -> key
                const parts = file.id.split('-existing-');
                return parts.length > 1 ? parts[1] : '';
              })
              .filter(key => key !== '');

            // Chỉ DELETE ảnh đã bị xóa khỏi version
            originalVersionImages.forEach((img: Record<string, unknown>) => {
              const imageKey = img['key'] as string;
              if (!currentVersionExistingIds.includes(imageKey)) {
                // Ảnh này đã bị xóa khỏi version
                versionImageOperations.push({
                  operation: 'DELETE',
                  key: imageKey, // Sử dụng key thay vì position
                });
              }
            });

            // Chỉ ADD ảnh mới (không phải existing/variant)
            const newVersionImages = (version.images || []).filter(file =>
              !file.id.startsWith('existing-') && !file.id.startsWith('variant-')
            );

            newVersionImages.forEach((file) => {
              versionImageOperations.push({
                operation: 'ADD',
                mimeType: file.file?.type || 'image/jpeg',
              });
            });

            return {
              name: version.name,
              sku: version.sku || `${version.name.toUpperCase().replace(/\s+/g, '-')}-${String(versionIndex + 1).padStart(3, '0')}`,
              availableQuantity: version.quantity || 1,
              minQuantityPerPurchase: version.minQuantityPerPurchase || 1,
              maxQuantityPerPurchase: version.maxQuantityPerPurchase || 1,
              price: {
                listPrice: version.price || 0,
                salePrice: Math.round((version.price || 0) * 0.9), // Giảm 10%
                currency: version.currency || 'VND',
              },
              // Chỉ thêm imageOperations nếu có thay đổi thực sự
              ...(versionImageOperations.length > 0 && {
                imageOperations: versionImageOperations
              }),
              description: version.description || `Phiên bản ${version.name} đã cập nhật`,
              customFields: version.customFields
                .filter(field => {
                  const fieldValue = field.value?.['value'];
                  return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
                })
                .map(field => ({
                  customFieldId: field.fieldId,
                  value: {
                    value: field.value?.['value'],
                  },
                })),
            };
          }) : [
            {
              name: 'Basic',
              sku: 'BASIC-002',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 1,
              price: {
                listPrice: typeof priceData === 'object' && priceData && 'listPrice' in priceData ? priceData.listPrice : 600000,
                salePrice: typeof priceData === 'object' && priceData && 'salePrice' in priceData ? priceData.salePrice : 500000,
                currency: typeof priceData === 'object' && priceData && 'currency' in priceData ? priceData.currency : 'VND',
              },
              // Không thêm imageOperations cho default variant nếu không cần thiết
              description: 'Phiên bản cơ bản đã cập nhật - Học React cơ bản',
              customFields: filteredCustomFields.slice(0, 3), // Lấy 3 custom fields đầu tiên cho Basic version
            }
          ]
        },
      };

      // Tạo request body theo format mới
      const productData: Record<string, unknown> = {
        name: formValues.name,
        price: priceData,
        advancedInfo, // Thông tin nâng cao cho sản phẩm số
      };

      // Thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData['description'] = formValues.description.trim();
      }

      if (tempTags && tempTags.length > 0) {
        productData['tags'] = tempTags;
      }

      // Thêm custom fields nếu có
      if (filteredCustomFields.length > 0) {
        productData['customFields'] = filteredCustomFields;
      }

      // Xử lý images operations (ADD/DELETE) cho sản phẩm chính ONLY
      const imageOperations: Record<string, unknown>[] = [];

      // Tìm ảnh đã bị xóa (existing images không còn trong mediaFiles)
      const originalImages = ((product as unknown as Record<string, unknown>)?.['images'] as Record<string, unknown>[]) || [];
      const currentExistingIds = mediaFiles
        .filter(file => file.id.startsWith('existing-'))
        .map(file => file.id);

      // Chỉ xử lý DELETE cho ảnh sản phẩm chính bị xóa
      originalImages.forEach((img: Record<string, unknown>, index: number) => {
        const existingId = `existing-${img['key']}-${index}`;
        if (!currentExistingIds.includes(existingId)) {
          // Ảnh này đã bị xóa khỏi sản phẩm chính
          imageOperations.push({
            operation: 'DELETE',
            key: img['key'] as string, // Sử dụng key thay vì position
          });
        }
      });

      // Chỉ thêm ảnh mới cho sản phẩm chính (không phải existing)
      const newMainImages = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      newMainImages.forEach((file) => {
        imageOperations.push({
          operation: 'ADD',
          mimeType: file.file.type || 'image/jpeg',
        });
      });

      // Chỉ thêm imageOperations nếu có thay đổi thực sự cho sản phẩm chính
      if (imageOperations.length > 0) {
        productData['imageOperations'] = imageOperations;
      }
      // Gọi API cập nhật sản phẩm số
      console.log('🔄 [DigitalProductEditForm] Updating digital product with data:', productData);
      const response = await updateDigitalProductMutation.mutateAsync({
        id: productId,
        data: productData as unknown as Parameters<typeof updateDigitalProductMutation.mutateAsync>[0]['data'],
      });
      console.log('✅ [DigitalProductEditForm] Digital product updated successfully:', response);

      // Upload media mới nếu có - TÁCH BIỆT hoàn toàn
      const uploadPromises: Promise<void>[] = [];

      // 1. Upload hình ảnh chính (CHỈ ảnh sản phẩm chính)
      const newMainImagesForUpload = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      console.log('🖼️ [Main Images] New main images to upload:', newMainImagesForUpload.length);

      if (newMainImagesForUpload.length > 0) {
        const hasMainUploadUrls =
          response &&
          typeof response === 'object' &&
          'uploadUrls' in response &&
          response['uploadUrls'] &&
          typeof response['uploadUrls'] === 'object' &&
          'imagesUploadUrls' in (response['uploadUrls'] as Record<string, unknown>) &&
          Array.isArray((response['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls']);

        if (hasMainUploadUrls && response['uploadUrls']) {
          const mainUploadUrls = (response['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls'] as Record<string, unknown>[];
          console.log('🔗 [Main Images] Available upload URLs:', mainUploadUrls.length);

          if (mainUploadUrls.length > 0) {
            const uploadMainImagesPromise = (async () => {
              console.log('📤 [Main Images] Starting upload for main product images...');

              const uploadTasks = newMainImagesForUpload.slice(0, mainUploadUrls.length).map((fileData, index) => {
                const uploadInfo = mainUploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Main upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                };
              });

              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_main_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ [Main Images] Main product images uploaded successfully');
            })();

            uploadPromises.push(uploadMainImagesPromise);
          }
        }
      }

      // 2. Upload hình ảnh variants (CHỈ ảnh mới của variants)
      const hasAdvancedUploadUrls =
        response &&
        typeof response === 'object' &&
        'uploadUrls' in response &&
        response['uploadUrls'] &&
        typeof response['uploadUrls'] === 'object' &&
        'advancedImagesUploadUrls' in (response['uploadUrls'] as Record<string, unknown>) &&
        Array.isArray((response['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls']);

      if (hasAdvancedUploadUrls && response['uploadUrls']) {
        const advancedUploadUrls = (response['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls'] as Record<string, unknown>[];
        console.log('🔗 [Variant Images] Available advanced upload URLs:', advancedUploadUrls.length);

        if (advancedUploadUrls.length > 0) {
          // Collect CHỈ ảnh mới của variants (không phải existing/variant)
          const allNewVariantImages: { file: File; versionName: string; versionIndex: number }[] = [];

          versions.forEach((version, versionIndex) => {
            // CHỈ lấy ảnh mới (không có prefix existing- hoặc variant-)
            const newVersionImages = (version.images || []).filter(file =>
              !file.id.startsWith('existing-') && !file.id.startsWith('variant-')
            );

            console.log(`🖼️ [Variant Images] Version "${version.name}" has ${newVersionImages.length} new images`);

            newVersionImages.forEach(fileData => {
              allNewVariantImages.push({
                file: fileData.file,
                versionName: version.name,
                versionIndex,
              });
            });
          });

          console.log('🖼️ [Variant Images] Total new variant images to upload:', allNewVariantImages.length);

          if (allNewVariantImages.length > 0) {
            const uploadVariantImagesPromise = (async () => {
              console.log('📤 [Variant Images] Starting upload for variant images...');

              const uploadTasks = allNewVariantImages.slice(0, advancedUploadUrls.length).map((fileData, index) => {
                const uploadInfo = advancedUploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Variant upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                  versionName: fileData.versionName,
                };
              });

              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_variant_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              console.log('✅ [Variant Images] Variant images uploaded successfully');
            })();

            uploadPromises.push(uploadVariantImagesPromise);
          }
        }
      }

      // Execute all upload promises
      if (uploadPromises.length > 0) {
        try {
          await Promise.all(uploadPromises);

          // Invalidate cache to refresh product data with new images
          console.log('🔄 [DigitalProductEditForm] Invalidating cache after image upload...');
          queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(productId) });

          NotificationUtil.success({
            message: t('business:product.mediaUploadSuccess', 'Tải lên ảnh sản phẩm thành công'),
            duration: 3000,
          });
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError', 'Có lỗi xảy ra khi tải lên ảnh sản phẩm'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sản phẩm số thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có với delay nhỏ để đảm bảo cache đã được refresh
      if (onSuccess) {
        // Delay 500ms để đảm bảo cache invalidation hoàn tất
        setTimeout(() => {
          onSuccess();
        }, 500);
      }
    } catch (error) {
      setIsUploading(false);
      console.error('❌ [DigitalProductEditForm] Error updating digital product:', error);

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        NotificationUtil.error({
          message: t('business:product.validationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sản phẩm số'),
          duration: 3000,
        });
      }
    }
  };

  return (
    <FormMultiWrapper>
      <Form
        ref={formRef}
        schema={digitalProductSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        onError={errors => {
          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
          });
          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        submitOnEnter={false}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <Typography variant="h5" className="font-semibold">
            {t('business:product.form.editDigitalTitle', 'Chỉnh sửa sản phẩm số')}
          </Typography>
        </div>

        {/* 1. Thông tin cơ bản */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.basicInfo', '1. Thông tin cơ bản')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                ]}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                    <Input fullWidth type="number" min="0" placeholder={t('business:product.form.listPricePlaceholder')} />
                  </FormItem>
                  <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                    <Input fullWidth type="number" min="0" placeholder={t('business:product.form.salePricePlaceholder')} />
                  </FormItem>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem name="currency" label={t('business:product.currency')} required>
                    <Controller
                      name="currency"
                      render={({ field }) => (
                        <Select
                          fullWidth
                          value={field.value || 'VND'}
                          onChange={value => field.onChange(value)}
                          options={[
                            { value: 'VND', label: 'VND' },
                            { value: 'USD', label: 'USD' },
                            { value: 'EUR', label: 'EUR' },
                          ]}
                        />
                      )}
                    />
                  </FormItem>
                </div>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '3. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 4. Quy trình xử lý đơn hàng số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalProcessing', '4. Quy trình xử lý đơn hàng số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="deliveryMethod" label={t('business:product.form.digitalProduct.deliveryMethod.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'EMAIL', label: t('business:product.form.digitalProduct.deliveryMethod.email') },
                  { value: 'DASHBOARD', label: t('business:product.form.digitalProduct.deliveryMethod.dashboardDownload') },
                  { value: 'SMS', label: t('business:product.form.digitalProduct.deliveryMethod.sms') },
                  { value: 'DIRECT_MESSAGE', label: t('business:product.form.digitalProduct.deliveryMethod.directMessage') },
                  { value: 'ZALO', label: t('business:product.form.digitalProduct.deliveryMethod.zalo') },
                  { value: 'AUTO_ACTIVE', label: t('business:product.form.digitalProduct.deliveryMethod.courseActivation') },
                ]}
              />
            </FormItem>

            <FormItem name="deliveryTiming" label={t('business:product.form.digitalProduct.deliveryTiming.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'IMMEDIATE', label: t('business:product.form.digitalProduct.deliveryTiming.immediate') },
                  { value: 'DELAYED', label: t('business:product.form.digitalProduct.deliveryTiming.delayed') },
                ]}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Đầu ra sản phẩm số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalOutput', '5. Đầu ra sản phẩm số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="digitalProductType" label={t('business:product.form.digitalProduct.digitalProductType.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'ACCESS_CODE', label: t('business:product.form.digitalProduct.digitalProductType.onlineCourse') },
                  { value: 'DOWNLOAD_LINK', label: t('business:product.form.digitalProduct.digitalProductType.fileDownload') },
                  { value: 'ACCOUNT_INFO', label: t('business:product.form.digitalProduct.digitalProductType.licenseKey') },
                  { value: 'CONTENT', label: t('business:product.form.digitalProduct.digitalProductType.ebook') },
                ]}
              />
            </FormItem>

            <FormItem name="accessLink" label={t('business:product.form.digitalProduct.downloadLink')}>
              <Input fullWidth placeholder={t('business:product.form.digitalProduct.downloadLinkPlaceholder')} />
            </FormItem>

            <FormItem name="usageInstructions" label={t('business:product.form.digitalProduct.usageInstructions')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.digitalProduct.usageInstructionsPlaceholder')}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 6. Phiên bản */}
        <CollapsibleCard
          title={
            <div className="flex items-center justify-between w-full">
              <Typography variant="h6" className="font-medium">
                {versions.length > 0
                  ? `6. ${t('business:product.form.versions.title', 'Phiên bản')} (${versions.length})`
                  : `6. ${t('business:product.form.versions.title', 'Phiên bản')}`
                }
              </Typography>
              <div
                onClick={e => {
                  e.stopPropagation();
                  handleAddVersion();
                }}
                className="cursor-pointer"
              >
                <IconCard
                  icon="plus"
                  title={t('business:product.form.versions.addVersion', 'Thêm phiên bản')}
                  variant="primary"
                  size="sm"
                />
              </div>
            </div>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {versions.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Typography variant="body2">
                  {t('business:product.form.versions.noVersions', 'Chưa có phiên bản nào. Nhấn "Thêm phiên bản" để bắt đầu.')}
                </Typography>
              </div>
            ) : (
              <div className="space-y-4">
                {versions.map((version, index) => (
                  <CollapsibleCard
                    key={version.id}
                    title={
                      <div className="flex justify-between items-center w-full">
                        <div className="flex items-center space-x-4">
                          <Typography variant="body2" className="font-medium">
                            {version.name || `Phiên bản ${index + 1}`}
                          </Typography>
                          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                            {version.price > 0 ? `${version.price.toLocaleString()} ${version.currency}` : '0 VND'}
                          </Typography>
                          <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                            {version.quantity ? `SL: ${version.quantity}` : 'SL: 1'}
                          </Typography>
                        </div>
                        <div
                          onClick={e => {
                            e.stopPropagation();
                            handleRemoveVersion(version.id!);
                          }}
                          className="cursor-pointer"
                        >
                          <IconCard
                            icon="trash"
                            title={t('business:product.form.versions.removeVersion', 'Xóa phiên bản')}
                            variant="danger"
                            size="sm"
                          />
                        </div>
                      </div>
                    }
                    defaultOpen={true}
                  >
                    <div className="space-y-4">
                      {/* Tên phiên bản và giá */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormItem label={t('business:product.form.versions.name', 'Tên phiên bản')} required>
                          <Input
                            fullWidth
                            placeholder={t('business:product.form.versions.namePlaceholder', 'Basic, Pro, Premium...')}
                            value={version.name}
                            onChange={(e) => handleUpdateVersion(version.id!, 'name', e.target.value)}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.price', 'Giá')} required>
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            placeholder="0"
                            value={version.price}
                            onChange={(e) => handleUpdateVersion(version.id!, 'price', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.currency', 'Đơn vị tiền tệ')} required>
                          <Select
                            fullWidth
                            value={version.currency || 'VND'}
                            onChange={(value) => {
                              const selectedValue = Array.isArray(value) ? value[0] : value;
                              handleUpdateVersion(version.id!, 'currency', selectedValue as string);
                            }}
                            options={[
                              { value: 'VND', label: 'VND' },
                              { value: 'USD', label: 'USD' },
                              { value: 'EUR', label: 'EUR' },
                            ]}
                          />
                        </FormItem>
                      </div>

                      {/* Mô tả phiên bản */}
                      <FormItem label={t('business:product.form.versions.description', 'Mô tả phiên bản')}>
                        <Textarea
                          fullWidth
                          rows={3}
                          placeholder={t('business:product.form.versions.descriptionPlaceholder', 'Mô tả chi tiết về phiên bản này...')}
                          value={version.description || ''}
                          onChange={(e) => handleUpdateVersion(version.id!, 'description', e.target.value)}
                        />
                      </FormItem>

                      {/* Số lượng và SKU */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem label={t('business:product.form.versions.quantity', 'Số lượng có sẵn')} required>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="100"
                            value={version.quantity}
                            onChange={(e) => handleUpdateVersion(version.id!, 'quantity', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.sku', 'Mã SKU')}>
                          <Input
                            fullWidth
                            placeholder={t('business:product.form.versions.skuPlaceholder', 'BASIC-001')}
                            value={version.sku || ''}
                            onChange={(e) => handleUpdateVersion(version.id!, 'sku', e.target.value)}
                          />
                        </FormItem>
                      </div>

                      {/* Số lượng mua tối thiểu và tối đa */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem label={t('business:product.form.versions.minQuantity', 'Số lượng tối thiểu mỗi lần mua')}>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="1"
                            value={version.minQuantityPerPurchase}
                            onChange={(e) => handleUpdateVersion(version.id!, 'minQuantityPerPurchase', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.maxQuantity', 'Số lượng tối đa mỗi lần mua')}>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="10"
                            value={version.maxQuantityPerPurchase}
                            onChange={(e) => handleUpdateVersion(version.id!, 'maxQuantityPerPurchase', Number(e.target.value))}
                          />
                        </FormItem>
                      </div>

                      {/* Hình ảnh phiên bản */}
                      <FormItem label={t('business:product.form.versions.images', 'Ảnh phiên bản')}>
                        <MultiFileUpload
                          value={version.images || []}
                          onChange={(files: FileWithMetadata[]) => {
                            // Convert FileWithMetadata to ExtendedFileWithMetadata
                            const extendedFiles: ExtendedFileWithMetadata[] = files.map(file => ({
                              ...file,
                              name: file.file.name,
                              size: file.file.size,
                              type: file.file.type,
                            }));
                            handleVersionImagesChange(version.id!, extendedFiles);
                          }}
                          accept="image/*"
                          mediaOnly={true}
                          placeholder={t(
                            'business:product.form.versions.imagesPlaceholder',
                            'Chọn ảnh cho phiên bản này'
                          )}
                          className="w-full"
                        />
                      </FormItem>

                      {/* Trường tùy chỉnh cho phiên bản */}
                      <div className="space-y-4">
                        <Typography variant="body2" className="font-medium">
                          {t('business:product.form.versions.customFields', 'Thuộc tính phiên bản')}
                        </Typography>

                        <SimpleCustomFieldSelector
                          onFieldSelect={fieldData => {
                            handleToggleCustomFieldToVersion(
                              version.id!,
                              fieldData.id,
                              fieldData as unknown as Record<string, unknown>
                            );
                          }}
                          selectedFieldIds={version.customFields.map(f => f.fieldId)}
                          placeholder={t(
                            'business:product.form.versions.searchCustomField',
                            'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                          )}
                        />

                        {version.customFields.length > 0 && (
                          <div className="space-y-3">
                            {version.customFields.map(field => {
                              const fieldValue = field.value?.['value'] as string | number | boolean || '';

                              return (
                                <CustomFieldRenderer
                                  key={field.id}
                                  field={field}
                                  value={fieldValue}
                                  onChange={(value: string | number | boolean) =>
                                    handleUpdateCustomFieldInVersion(version.id!, field.id, value)
                                  }
                                  onRemove={() => handleRemoveCustomFieldFromVersion(version.id!, field.id)}
                                />
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  </CollapsibleCard>
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'business:product.form.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-3">
                {productCustomFields.map(field => {
                  const fieldValue = field.value?.['value'] as string | number | boolean || '';

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={field}
                      value={fieldValue}
                      onChange={(value: string | number | boolean) =>
                        handleUpdateCustomFieldInProduct(field.id, value)
                      }
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              updateDigitalProductMutation.isPending || isUploading
                ? t('business:product.form.updating', 'Đang cập nhật...')
                : t('business:product.form.update', 'Cập nhật sản phẩm')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={updateDigitalProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default DigitalProductEditForm;
