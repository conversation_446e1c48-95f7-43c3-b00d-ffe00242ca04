import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Select,
} from '@/shared/components/common';
import { DigitalProcessingSectionProps } from './digital-product-form-types';

const DigitalProcessingSection: React.FC<DigitalProcessingSectionProps> = () => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.digitalProcessing', '3. Quy trình xử lý đơn hàng số')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="deliveryMethod" label={t('business:product.form.digitalProduct.deliveryMethod.title')} required>
          <Select
            fullWidth
            options={[
              { value: 'EMAIL', label: t('business:product.form.digitalProduct.deliveryMethod.email') },
              { value: 'DASHBOARD', label: t('business:product.form.digitalProduct.deliveryMethod.dashboardDownload') },
              { value: 'SMS', label: t('business:product.form.digitalProduct.deliveryMethod.sms') },
              { value: 'DIRECT_MESSAGE', label: t('business:product.form.digitalProduct.deliveryMethod.directMessage') },
              { value: 'ZALO', label: t('business:product.form.digitalProduct.deliveryMethod.zalo') },
              { value: 'AUTO_ACTIVE', label: t('business:product.form.digitalProduct.deliveryMethod.courseActivation') },
            ]}
          />
        </FormItem>

        <FormItem name="deliveryTiming" label={t('business:product.form.digitalProduct.deliveryTiming.title')} required>
          <Select
            fullWidth
            options={[
              { value: 'IMMEDIATE', label: t('business:product.form.digitalProduct.deliveryTiming.immediate') },
              { value: 'DELAYED', label: t('business:product.form.digitalProduct.deliveryTiming.delayed') },
            ]}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default DigitalProcessingSection;
